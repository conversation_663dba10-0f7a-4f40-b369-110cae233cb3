<?php
// Base theme functions
include_once 'assets/php/theme-setup.php';
include_once 'assets/php/theme-options.php';
include_once 'assets/php/woocommerce-setup.php';
include_once 'assets/php/security-headers.php';
include_once 'components/index.php';


/* Auto-load Smort Blocks */
//Automatically include all block registration files
$smort_blocks_dir = get_stylesheet_directory() . '/template-parts/smortblocks/';
if (is_dir($smort_blocks_dir)) {
	$register_path = '/register.php';
	$block_dirs = glob($smort_blocks_dir . '*', GLOB_ONLYDIR);
	foreach ($block_dirs as $block_dir) {
		$register_file = $block_dir . $register_path;
		if (file_exists($register_file)) {
			require_once $register_file;
		}
	}
}

include_once 'assets/php/acf-blocks.php';
