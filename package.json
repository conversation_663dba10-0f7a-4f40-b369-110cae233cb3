{"name": "smort-tema", "version": "1.0.0", "main": "index.js", "scripts": {"start": "browser-sync start --proxy http://parent-theme-smort.local / --files 'assets/css/*.css, assets/js/*.js, **/*.html, **/*.php' --ignore 'node_modules'", "css:build": "postcss assets/css/main.css -o assets/css/main.min.css --env production"}, "keywords": [], "license": "ISC", "dependencies": {"browser-sync": "^3.0.2"}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.1"}, "description": ""}