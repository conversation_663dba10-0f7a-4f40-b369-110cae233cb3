// ==========================================================================
// GSAP ANIMATIONS
// ==========================================================================

export const GSAPAnimations = {
  init() {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger, TextPlugin);

    // Initialize animations
    this.fadeInUp();
    this.fadeInLeft();
    this.fadeInRight();
    this.staggerAnimation();
    this.textReveal();
    this.parallaxElements();
  },

  // Fade in up animation
  fadeInUp() {
    gsap.fromTo(
      ".gsap-fade-up",
      {
        opacity: 0,
        y: 50,
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".gsap-fade-up",
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      }
    );
  },

  // Fade in from left
  fadeInLeft() {
    gsap.fromTo(
      ".gsap-fade-left",
      {
        opacity: 0,
        x: -50,
      },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".gsap-fade-left",
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      }
    );
  },

  // Fade in from right
  fadeInRight() {
    gsap.fromTo(
      ".gsap-fade-right",
      {
        opacity: 0,
        x: 50,
      },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".gsap-fade-right",
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      }
    );
  },

  // Stagger animation for multiple elements
  staggerAnimation() {
    gsap.fromTo(
      ".gsap-stagger",
      {
        opacity: 0,
        y: 30,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: ".gsap-stagger",
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      }
    );
  },

  // Text reveal animation
  textReveal() {
    gsap.fromTo(
      ".gsap-text-reveal",
      {
        opacity: 0,
        clipPath: "inset(0 100% 0 0)",
      },
      {
        opacity: 1,
        clipPath: "inset(0 0% 0 0)",
        duration: 1.5,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".gsap-text-reveal",
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      }
    );
  },

  // Parallax elements
  parallaxElements() {
    gsap.utils.toArray(".gsap-parallax").forEach((element) => {
      gsap.fromTo(
        element,
        {
          y: -50,
        },
        {
          y: 50,
          ease: "none",
          scrollTrigger: {
            trigger: element,
            start: "top bottom",
            end: "bottom top",
            scrub: true,
          },
        }
      );
    });
  },

  // Custom timeline animation
  createTimeline(trigger, animations) {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: trigger,
        start: "top 80%",
        toggleActions: "play none none reverse",
      },
    });

    animations.forEach((anim) => {
      tl.fromTo(anim.element, anim.from, anim.to, anim.position || ">");
    });

    return tl;
  },
};
