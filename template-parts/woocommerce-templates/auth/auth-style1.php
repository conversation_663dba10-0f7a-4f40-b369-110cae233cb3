<main class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900">
                <?php esc_html_e('Sign in to your account', 'woocommerce'); ?>
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                <?php esc_html_e('Or register for a new account', 'woocommerce'); ?>
            </p>
        </div>
        
        <?php do_action('woocommerce_before_customer_login_form'); ?>
        
        <div class="bg-white rounded-lg shadow-sm p-8">
            <?php if ('yes' === get_option('woocommerce_enable_myaccount_registration')) : ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Login Form -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4"><?php esc_html_e('Login', 'woocommerce'); ?></h3>
                        
                        <form class="woocommerce-form woocommerce-form-login login space-y-4" method="post">
                            
                            <?php do_action('woocommerce_login_form_start'); ?>
                            
                            <div>
                                <label for="username" class="block text-sm font-medium text-gray-700">
                                    <?php esc_html_e('Username or email address', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                                </label>
                                <input type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" name="username" id="username" autocomplete="username" value="<?php echo (!empty($_POST['username'])) ? esc_attr(wp_unslash($_POST['username'])) : ''; ?>" required />
                            </div>
                            
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700">
                                    <?php esc_html_e('Password', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                                </label>
                                <input class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" type="password" name="password" id="password" autocomplete="current-password" required />
                            </div>
                            
                            <?php do_action('woocommerce_login_form'); ?>
                            
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input name="rememberme" type="checkbox" id="rememberme" value="forever" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" />
                                    <span class="ml-2 text-sm text-gray-600"><?php esc_html_e('Remember me', 'woocommerce'); ?></span>
                                </label>
                            </div>
                            
                            <div>
                                <?php wp_nonce_field('woocommerce-login', 'woocommerce-login-nonce'); ?>
                                <button type="submit" class="btn btn-primary w-full" name="login" value="<?php esc_attr_e('Log in', 'woocommerce'); ?>">
                                    <?php esc_html_e('Log in', 'woocommerce'); ?>
                                </button>
                            </div>
                            
                            <div class="text-center">
                                <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" class="text-sm text-primary hover:underline">
                                    <?php esc_html_e('Lost your password?', 'woocommerce'); ?>
                                </a>
                            </div>
                            
                            <?php do_action('woocommerce_login_form_end'); ?>
                            
                        </form>
                    </div>
                    
                    <!-- Register Form -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4"><?php esc_html_e('Register', 'woocommerce'); ?></h3>
                        
                        <form method="post" class="woocommerce-form woocommerce-form-register register space-y-4" <?php do_action('woocommerce_register_form_tag'); ?>>
                            
                            <?php do_action('woocommerce_register_form_start'); ?>
                            
                            <?php if ('no' === get_option('woocommerce_registration_generate_username')) : ?>
                                <div>
                                    <label for="reg_username" class="block text-sm font-medium text-gray-700">
                                        <?php esc_html_e('Username', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                                    </label>
                                    <input type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" name="username" id="reg_username" autocomplete="username" value="<?php echo (!empty($_POST['username'])) ? esc_attr(wp_unslash($_POST['username'])) : ''; ?>" required />
                                </div>
                            <?php endif; ?>
                            
                            <div>
                                <label for="reg_email" class="block text-sm font-medium text-gray-700">
                                    <?php esc_html_e('Email address', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                                </label>
                                <input type="email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" name="email" id="reg_email" autocomplete="email" value="<?php echo (!empty($_POST['email'])) ? esc_attr(wp_unslash($_POST['email'])) : ''; ?>" required />
                            </div>
                            
                            <?php if ('no' === get_option('woocommerce_registration_generate_password')) : ?>
                                <div>
                                    <label for="reg_password" class="block text-sm font-medium text-gray-700">
                                        <?php esc_html_e('Password', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                                    </label>
                                    <input type="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" name="password" id="reg_password" autocomplete="new-password" required />
                                </div>
                            <?php endif; ?>
                            
                            <?php do_action('woocommerce_register_form'); ?>
                            
                            <div>
                                <?php wp_nonce_field('woocommerce-register', 'woocommerce-register-nonce'); ?>
                                <button type="submit" class="btn btn-secondary w-full" name="register" value="<?php esc_attr_e('Register', 'woocommerce'); ?>">
                                    <?php esc_html_e('Register', 'woocommerce'); ?>
                                </button>
                            </div>
                            
                            <?php do_action('woocommerce_register_form_end'); ?>
                            
                        </form>
                    </div>
                </div>
                
            <?php else : ?>
                
                <!-- Login Only -->
                <div class="max-w-sm mx-auto">
                    <h3 class="text-lg font-semibold mb-4 text-center"><?php esc_html_e('Login', 'woocommerce'); ?></h3>
                    
                    <form class="woocommerce-form woocommerce-form-login login space-y-4" method="post">
                        
                        <?php do_action('woocommerce_login_form_start'); ?>
                        
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">
                                <?php esc_html_e('Username or email address', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                            </label>
                            <input type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" name="username" id="username" autocomplete="username" value="<?php echo (!empty($_POST['username'])) ? esc_attr(wp_unslash($_POST['username'])) : ''; ?>" required />
                        </div>
                        
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                <?php esc_html_e('Password', 'woocommerce'); ?>&nbsp;<span class="required text-red-500">*</span>
                            </label>
                            <input class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary" type="password" name="password" id="password" autocomplete="current-password" required />
                        </div>
                        
                        <?php do_action('woocommerce_login_form'); ?>
                        
                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input name="rememberme" type="checkbox" id="rememberme" value="forever" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" />
                                <span class="ml-2 text-sm text-gray-600"><?php esc_html_e('Remember me', 'woocommerce'); ?></span>
                            </label>
                        </div>
                        
                        <div>
                            <?php wp_nonce_field('woocommerce-login', 'woocommerce-login-nonce'); ?>
                            <button type="submit" class="btn btn-primary w-full" name="login" value="<?php esc_attr_e('Log in', 'woocommerce'); ?>">
                                <?php esc_html_e('Log in', 'woocommerce'); ?>
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" class="text-sm text-primary hover:underline">
                                <?php esc_html_e('Lost your password?', 'woocommerce'); ?>
                            </a>
                        </div>
                        
                        <?php do_action('woocommerce_login_form_end'); ?>
                        
                    </form>
                </div>
                
            <?php endif; ?>
        </div>
        
        <?php do_action('woocommerce_after_customer_login_form'); ?>
    </div>
</main>