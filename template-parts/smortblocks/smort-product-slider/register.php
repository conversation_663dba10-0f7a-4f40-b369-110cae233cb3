<?php

/**
 * Register Smort Product Slider Block
 */


// Register the block
if (function_exists('acf_register_block_type')) {
  acf_register_block_type(array(
    'name' => 'smort-product-slider',
    'title' => __("Smort - Smort Product Slider"),
    'description' => __('Smort block'),
    'render_template' => '/template-parts/smortblocks/smort-product-slider/smort-product-slider.php',
    'category' => 'smort',
    'icon' => 'id',
    'keywords' => array('CTA', 'Block'),
    'mode' => 'edit',
    'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.css',
    'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.js'
  ));
}
// Include ACF field group
require_once __DIR__ . '/acf/smort-product-slider.php';
