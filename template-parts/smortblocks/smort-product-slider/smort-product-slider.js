/**
 * smort Product Slider JavaScript
 * Hanterar horisontell scrollning, dragging och progress bar
 */
document.addEventListener('DOMContentLoaded', function() {
    initProductSliders();

    // Initiera även när nya block läggs till i redigeraren
    if (window.acf) {
        window.acf.addAction('render_block_preview', function() {
            initProductSliders();
        });
    }
});

/**
 * Initierar alla produktsliders på sidan
 */
function initProductSliders() {
    const sliders = document.querySelectorAll('.smort-product-slider');

    sliders.forEach(slider => {
        const track = slider.querySelector('.smort-product-slider-track');
        const progressBar = slider.querySelector('.smort-product-progress-bar');
        const prevButton = slider.querySelector('.smort-product-nav-prev');
        const nextButton = slider.querySelector('.smort-product-nav-next');

        if (!track) {
            return;
        }

        // Lyssna på scroll-händelser för att uppdatera progress bar
        track.addEventListener('scroll', function() {
            updateProgressBar(track, progressBar);
        });

        // Gör slidern draggable
        makeDraggable(track);

        // Initiera progress bar med lite fyllning från början
        if (progressBar) {
            progressBar.style.width = '15%'; // Starta med 15% fyllning
        }

        // Lägg till klickhändelser för navigationspilar
        if (prevButton) {
            prevButton.addEventListener('click', function() {
                scrollSlider(track, 'prev');
            });
        }

        if (nextButton) {
            nextButton.addEventListener('click', function() {
                scrollSlider(track, 'next');
            });
        }
    });
}

/**
 * Uppdaterar progress bar baserat på aktuell scroll-position
 */
function updateProgressBar(track, progressBar) {
    if (!progressBar) return;

    // Beräkna hur långt användaren har scrollat i procent
    const scrollPosition = track.scrollLeft;
    const maxScroll = track.scrollWidth - track.clientWidth;
    const scrollPercentage = (scrollPosition / maxScroll) * 100;

    // Uppdatera progress bar width
    progressBar.style.width = `${scrollPercentage}%`;
}

/**
 * Scrollar slidern i angiven riktning
 */
function scrollSlider(track, direction) {
    // Beräkna scrollavstånd baserat på produktbredd
    const productItem = track.querySelector('.smort-product-item');
    if (!productItem) return;

    // Använd produktens bredd plus gap som scrollavstånd
    const itemWidth = productItem.offsetWidth;
    const gap = 10; // Samma som i CSS

    // Kontrollera om vi är i mobil (480px eller mindre)
    const isMobile = window.innerWidth <= 480;

    // I mobil: scrolla 2 produkter åt gången, annars 1
    const itemsToScroll = isMobile ? 2 : 1;
    const scrollDistance = (itemWidth + gap) * itemsToScroll;

    // Nuvarande scroll-position
    const currentScroll = track.scrollLeft;

    // Beräkna ny scroll-position baserat på riktning
    let newScroll;
    if (direction === 'next') {
        newScroll = currentScroll + scrollDistance;
    } else {
        newScroll = currentScroll - scrollDistance;
    }

    // Scrolla till den nya positionen med smooth scrolling
    track.scrollTo({
        left: newScroll,
        behavior: 'smooth'
    });
}

/**
 * Gör slidern draggable med musen
 */
function makeDraggable(track) {
    let isDown = false;
    let startX;
    let scrollLeft;
    let isDragging = false;
    let dragThreshold = 5; // Antal pixlar som krävs för att räknas som drag istället för klick

    // Mousedown event
    track.addEventListener('mousedown', (e) => {
        isDown = true;
        isDragging = false;
        track.classList.add('active');
        startX = e.pageX - track.offsetLeft;
        scrollLeft = track.scrollLeft;
        e.preventDefault();
    });

    // Mouseleave event
    track.addEventListener('mouseleave', () => {
        isDown = false;
        track.classList.remove('active');
    });

    // Mouseup event
    track.addEventListener('mouseup', () => {
        isDown = false;
        track.classList.remove('active');

        // Om det inte var en dragrörelse, låt klicket gå igenom
        if (!isDragging) {
            // Gör ingenting, låt klicket gå igenom till länken
        }
    });

    // Mousemove event
    track.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - track.offsetLeft;
        const walk = (x - startX);

        // Om användaren har dragit mer än tröskelvärdet, räknas det som en dragrörelse
        if (Math.abs(walk) > dragThreshold) {
            isDragging = true;
        }

        track.scrollLeft = scrollLeft - walk;
    });

    // Touch events för mobil
    track.addEventListener('touchstart', (e) => {
        isDown = true;
        isDragging = false;
        startX = e.touches[0].pageX - track.offsetLeft;
        scrollLeft = track.scrollLeft;
    }, { passive: true });

    track.addEventListener('touchend', () => {
        isDown = false;
    }, { passive: true });

    track.addEventListener('touchmove', (e) => {
        if (!isDown) return;
        const x = e.touches[0].pageX - track.offsetLeft;
        const walk = (x - startX);

        // Om användaren har dragit mer än tröskelvärdet, räknas det som en dragrörelse
        if (Math.abs(walk) > dragThreshold) {
            isDragging = true;
        }

        track.scrollLeft = scrollLeft - walk;
    }, { passive: true });
}