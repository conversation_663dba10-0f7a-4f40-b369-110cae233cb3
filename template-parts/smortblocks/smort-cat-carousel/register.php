<?php

/**
 * Register Smort Cate Carousel Block
 */


// Register the block
if (function_exists('acf_register_block_type')) {
  acf_register_block_type(array(
    'name' => 'smort-cat-carousel',
    'title' => __("Smort - Smort Cat Carousel"),
    'description' => __('Smort block'),
    'render_template' => '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.php',
    'category' => 'smort',
    'icon' => 'id',
    'keywords' => array('CTA', 'Block'),
    'mode' => 'edit',
    'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.css',
    'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.js'
  ));
}
// Include ACF field group
require_once __DIR__ . '/acf/smort-cat-carousel.php';
