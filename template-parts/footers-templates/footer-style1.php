<footer class="bg-gray-900 text-white">
  <div class="container mx-auto px-4 py-16">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">

      <!-- Company Info -->
      <div class="md:col-span-2">
        <a href="<?php echo get_bloginfo('url'); ?>" class="text-2xl font-bold mb-4 block">
          <?php if (get_field('footer_logo', 'option')): ?>
            <img class="h-10" src="<?= get_field('footer_logo', 'option')['url'] ?>" alt="<?= get_bloginfo('name') ?>">
          <?php else: ?>
            <?php echo get_bloginfo('name'); ?>
          <?php endif; ?>
        </a>
        <p class="text-gray-300 mb-6 max-w-md">
          <?php echo get_bloginfo('description'); ?>
        </p>

        <!-- Social Media -->
        <div class="flex space-x-4">
          <?php if (get_field('facebook_url', 'option')): ?>
            <a href="<?php the_field('facebook_url', 'option'); ?>" class="text-gray-400 hover:text-white">
              <i class="fab fa-facebook-f"></i>
            </a>
          <?php endif; ?>

          <?php if (get_field('instagram_url', 'option')): ?>
            <a href="<?php the_field('instagram_url', 'option'); ?>" class="text-gray-400 hover:text-white">
              <i class="fab fa-instagram"></i>
            </a>
          <?php endif; ?>

          <?php if (get_field('linkedin_url', 'option')): ?>
            <a href="<?php the_field('linkedin_url', 'option'); ?>" class="text-gray-400 hover:text-white">
              <i class="fab fa-linkedin-in"></i>
            </a>
          <?php endif; ?>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h4 class="font-semibold mb-4 text-lg">Snabblänkar</h4>
        <?php wp_nav_menu(array(
          'theme_location' => 'footer-menu',
          'container' => false,
          'menu_class' => 'footer-menu text-gray-300',
          'fallback_cb' => false
        )); ?>
      </div>

      <!-- Contact Info -->
      <div>
        <h4 class="font-semibold mb-4 text-lg">Kontakt</h4>
        <div class="text-gray-300">
          <?php if (get_field('phone', 'option')): ?>
            <p class="mb-2">
              <i class="fas fa-phone mr-2"></i>
              <?php the_field('phone', 'option'); ?>
            </p>
          <?php endif; ?>

          <?php if (get_field('email', 'option')): ?>
            <p class="mb-2">
              <i class="fas fa-envelope mr-2"></i>
              <?php the_field('email', 'option'); ?>
            </p>
          <?php endif; ?>

          <?php if (get_field('address', 'option')): ?>
            <p>
              <i class="fas fa-map-marker-alt mr-2"></i>
              <?php the_field('address', 'option'); ?>
            </p>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Bar -->
  <div class="bg-black py-4">
    <div class="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center text-gray-400 text-sm">
      <p>&copy; <?php echo date('Y'); ?> <?php echo get_bloginfo('name'); ?>. Alla rättigheter förbehållna.</p>
      <div class="flex space-x-4 mt-2 md:mt-0">
        <a href="/integritetspolicy" class="hover:text-white">Integritetspolicy</a>
        <a href="/villkor" class="hover:text-white">Villkor</a>
      </div>
    </div>
  </div>
</footer>