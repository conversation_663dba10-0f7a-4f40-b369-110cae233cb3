<?php

/**
 * Button Component
 * 
 * @param string $example Example prop
 * 
 */

// Set default values for props
// $example = $props['key'] ?? 'default';
$href = $props['href'] ?? '';
$text = $props['text'] ?? '<PERSON><PERSON><PERSON> mer';
$target = $props['target'] ?? '_self';
$type = $props['type'] ?? 'primary';
$size = $props['size'] ?? 'medium';
$disabled = $props['disabled'] ?? false;
$class = $props['class'] ?? '';

// Build CSS classes
$prefix = 'button';
$css_classes = [$prefix, $prefix . '-' . $type, $prefix . '-' . $size];
$css_classes .= $class;
// Alternative CSS Class building

// Get button.css styling
wp_enqueue_style('button-style', get_stylesheet_directory_uri() . '/components/button/button.css', array(), '1.0', 'all');

if (!empty($class)) {
  $css_classes[] = $class;
}

$class_string = implode(' ', $css_classes);

// Build attributes
$attributes = [];

if (!empty($id)) {
  $attributes[] = "id=\"{$id}\"";
}

if (!empty($onclick)) {
  $attributes[] = "onclick=\"{$onclick}\"";
}

if ($disabled) {
  $attributes[] = 'disabled';
}

$attributes_string = implode(' ', $attributes);

// Render the component
?>

<?php if (!empty($href)) : ?>
  <a href="<?php echo esc_url($href); ?>" target="<?= $target ?>" class="<?php echo esc_attr($class_string); ?>" <?php echo $attributes_string; ?>>
    <?= $text ?>
  </a>
<?php else : ?>
  <button class="<?php echo esc_attr($class_string); ?>" <?php echo $attributes_string; ?>>
    <?= $text ?>
  </button>
<?php endif; ?>