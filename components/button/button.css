/**
 * Button Block Styles
 */

 /* Main Button container */
.button {
    /* Add your styles here */
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    text-decoration: none;
    color: var(--text-color);
    font-size: var(--font-size-md);
    font-weight: 700;
    line-height: 1.5;
    transition: all 0.2s ease;
  }

/* Button Prop styles */
.button-primary {
  /* Add your styles here */
  background-color: var(--primary-color);
}

.button-secondary {
  /* Add your styles here */
  background-color: var(--secondary-color);
}

.button-small {
  /* Add your styles here */
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.button-medium {
  /* Add your styles here */
  padding: var(--spacing-md) var(--spacing-lg);
}

.button-large {
  /* Add your styles here */
  padding: var(--spacing-lg) var(--spacing-xl);
}

/* Responsive styles */
@media (width <= 1024px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}
@media (width <= 768px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}
@media (width <= 480px) {
  /* Add your styles here */
    .button {
      /* Add your styles here */
    }
}

