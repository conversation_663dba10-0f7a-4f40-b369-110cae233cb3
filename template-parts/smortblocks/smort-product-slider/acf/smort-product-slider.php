<?php

/**
 * ACF-fält för SmortProduct Slider Block
 */

if (function_exists('acf_add_local_field_group')) {
  acf_add_local_field_group(array(
    'key' => 'group_smort_product_slider',
    'title' => 'Smort Product Slider',
    'name' => 'smort_product_slider',
    'fields' => array(
      array(
        'key' => 'field_smort_product_slider_title',
        'label' => 'Titel',
        'name' => 'title',
        'type' => 'text',
        'instructions' => 'Ange en titel för produktsliderns sektion.',
        'default_value' => 'Våra senaste produkter',
        'placeholder' => 'Våra senaste produkter',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
      array(
        'key' => 'field_smort_product_slider_title',
        'label' => 'Subtitle',
        'name' => 'subtitle',
        'type' => 'text',
        'instructions' => 'Ange en subtitel för produktsliderns sektion.',
        'default_value' => 'Nyheter',
        'placeholder' => 'Nyheter',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
      array(
        'key' => 'field_smort_product_slider_selected_products',
        'label' => 'Välj specifika produkter',
        'name' => 'selected_products',
        'type' => 'relationship',
        'instructions' => 'Välj specifika produkter att visa i slidern. Lämna tomt för att använda kategorifiltrering istället.',
        'required' => 0,
        'post_type' => array('product'),
        'taxonomy' => '',
        'filters' => array('search'),
        'elements' => array('featured_image'),
        'min' => 0,
        'max' => '',
        'return_format' => 'id',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
      array(
        'key' => 'field_smort_product_slider_count',
        'label' => 'Antal produkter',
        'name' => 'product_count',
        'type' => 'number',
        'instructions' => 'Ange hur många produkter som ska visas i slidern (används endast om inga specifika produkter är valda).',
        'default_value' => 8,
        'min' => 1,
        'max' => 20,
        'step' => 1,
        'wrapper' => array(
          'width' => '50',
          'class' => '',
          'id' => '',
        ),
        'conditional_logic' => array(
          array(
            array(
              'field' => 'field_smort_product_slider_selected_products',
              'operator' => '==empty',
            ),
          ),
        ),
      ),
      array(
        'key' => 'field_smort_product_slider_category',
        'label' => 'Produktkategorier',
        'name' => 'product_categories',
        'type' => 'taxonomy',
        'instructions' => 'Välj produktkategorier att visa produkter från (används endast om inga specifika produkter är valda).',
        'taxonomy' => 'product_cat',
        'field_type' => 'multi_select',
        'allow_null' => 1,
        'add_term' => 0,
        'save_terms' => 0,
        'load_terms' => 0,
        'return_format' => 'id',
        'wrapper' => array(
          'width' => '50',
          'class' => '',
          'id' => '',
        ),
        'conditional_logic' => array(
          array(
            array(
              'field' => 'field_smort_product_slider_selected_products',
              'operator' => '==empty',
            ),
          ),
        ),
      ),
      array(
        'key' => 'field_smort_product_slider_button_text',
        'label' => 'Knapptext',
        'name' => 'button_text',
        'type' => 'text',
        'instructions' => 'Ange texten som ska visas på knappen vid hover.',
        'default_value' => 'Till produkten',
        'placeholder' => 'Till produkten',
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
      ),
    ),
    'location' => array(
      array(
        array(
          'param' => 'block',
          'operator' => '==',
          'value' => 'acf/smort-product-slider',
        ),
      ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
    'show_in_rest' => 0,
  ));
}
