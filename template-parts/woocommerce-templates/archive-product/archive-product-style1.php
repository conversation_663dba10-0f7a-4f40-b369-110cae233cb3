<main class="container mx-auto px-4 py-8">
    <?php if (woocommerce_product_loop()) : ?>

        <header class="woocommerce-products-header mb-8">
            <?php if (apply_filters('woocommerce_show_page_title', true)) : ?>
                <h1 class="text-4xl font-bold mb-4"><?php woocommerce_page_title(); ?></h1>
                <h2>ARKIV TEST </h2>
            <?php endif; ?>
            <?php do_action('woocommerce_archive_description'); ?>
        </header>

        <div class="flex justify-between items-center mb-8">
            <?php woocommerce_result_count(); ?>
            <?php woocommerce_catalog_ordering(); ?>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
            if (wc_get_loop_prop('is_shortcode')) {
                woocommerce_product_loop_start();
            }

            if (have_posts()) {
                while (have_posts()) {
                    the_post();
                    wc_get_template_part('content', 'product');
                }
            }

            if (wc_get_loop_prop('is_shortcode')) {
                woocommerce_product_loop_end();
            }
            ?>
        </div>

        <?php woocommerce_pagination(); ?>

    <?php else : ?>
        <?php do_action('woocommerce_no_products_found'); ?>
    <?php endif; ?>
</main>