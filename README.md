# Smort AB Parent Theme

Ett modernt WordPress parent tema byggt med ett eget CSS-bibliotek, ACF Flexible Content och optimerat för utveckling med live-reload funktionalitet.

## 📋 Funktioner

- **WordPress Parent Theme** - Komplett tema struktur för child themes
- **ACF Flexible Content** - Modulär sitebuilder med blocks
- **Smort CSS Library** - Eget utility-first CSS framework
- **PostCSS** - CSS preprocessor med autoprefixer och import
- **Browser-sync** - Live reload och proxy för lokal utveckling
- **Responsive Design** - Mobile-first approach med custom breakpoints
- **Custom Typography** - Anpassade fontstorlekar för desktop/mobile
- **WooCommerce Support** - Komplett e-handel integration med anpassade templates
- **Lazy Loading** - Automatisk bildoptimering
- **Säkerhetsheaders** - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> säkerhetsskydd
- **ACF Theme Options** - Centraliserade tema-inställningar

## 🛠 Dependencies

### Production Dependencies

- `browser-sync` (^3.0.2) - Live reload server

### Development Dependencies

- `postcss-cli` (^11.0.0) - PostCSS command line interface
- `postcss-import` (^16.1.1) - CSS import processor
- `autoprefixer` (^10.4.19) - CSS vendor prefixes
- `concurrently` (^8.2.2) - Kör flera kommandon samtidigt
- `npm-run-all` (^4.1.5) - NPM script runner
- `vite` (^5.3.3) - Build tool

### WordPress Plugin Dependencies

- **Advanced Custom Fields PRO** - Krävs för flexible content och theme options

### External CDN Libraries

- **Font Awesome** (5.15.4) - Ikoner
- **AOS** (Animate On Scroll) - Scroll-animationer
- **Swiper.js** (11.x) - Touch sliders och carousels
- **GSAP** (3.12.2) - Avancerade animationer och ScrollTrigger
- **Google Fonts** - Prompt typsnitt som standard

## 🎨 GSAP Animations

Temat inkluderar GSAP med ScrollTrigger för avancerade animationer:

### Tillgängliga CSS-klasser:

```html
<!-- Fade in up -->
<div class="gsap-fade-up">Content</div>

<!-- Fade in from left -->
<div class="gsap-fade-left">Content</div>

<!-- Fade in from right -->
<div class="gsap-fade-right">Content</div>

<!-- Stagger animation -->
<div class="gsap-stagger">Item 1</div>
<div class="gsap-stagger">Item 2</div>

<!-- Text reveal -->
<h1 class="gsap-text-reveal">Reveal text</h1>

<!-- Parallax effect -->
<div class="gsap-parallax">Parallax element</div>
```

### Custom animationer:

```js
// Skapa egen timeline
GSAPAnimations.createTimeline(".my-trigger", [
  {
    element: ".element1",
    from: { opacity: 0, x: -50 },
    to: { opacity: 1, x: 0, duration: 1 },
  },
  {
    element: ".element2",
    from: { opacity: 0, y: 30 },
    to: { opacity: 1, y: 0, duration: 0.8 },
    position: "-=0.5",
  },
]);
```

## 🚀 Installation & Setup

### 1. Installera WordPress plugins

```bash
# Installera ACF PRO (krävs)
# Ladda ner från: https://www.advancedcustomfields.com/pro/
```

### 2. Klona/ladda ner temat

```bash
# Placera temat i din WordPress themes mapp
wp-content/themes/smort-tema/
```

### 3. Installera Node.js dependencies

```bash
cd wp-content/themes/smort-tema
npm install
```

### 4. Konfigurera Browser-sync

Uppdatera proxy URL i `package.json` för din lokala utvecklingsmiljö:

```json
"start": "browser-sync start --proxy http://DIN-LOKALA-URL.local / --files 'assets/css/*.css, assets/js/*.js, **/*.html, **/*.php' --ignore 'node_modules'"
```

### 5. Starta utvecklingsservern

```bash
npm start
```

### 6. Konfigurera ACF

1. Gå till **WordPress Admin → Tema** för tema-inställningar
2. Skapa sidor med **Sitebuilder** flexible content field
3. Lägg till blocks: Hero, Text, etc.

## 📁 Filstruktur

```
smort-tema/
├── assets/
│   ├── css/
│   │   ├── main.css               # Huvudfil som importerar allt
│   │   ├── components.css         # Komponent-specifik CSS
│   │   ├── foundations/           # Grundläggande CSS
│   │   │   ├── reset.css         # CSS reset
│   │   │   ├── variables.css     # CSS-variabler
│   │   │   ├── typography.css    # Typografi-variabler
│   │   │   ├── colors.css        # Färg-variabler
│   │   │   ├── spacing.css       # Spacing-variabler
│   │   │   └── breakpoints.css   # Breakpoint-variabler
│   │   ├── layout/               # Layout-system
│   │   │   ├── container.css     # Container-system
│   │   │   ├── grid.css          # Grid-system
│   │   │   ├── flexbox.css       # Flexbox utilities
│   │   │   └── positioning.css   # Position utilities
│   │   ├── components/           # UI-komponenter
│   │   │   ├── buttons.css       # Knappar
│   │   │   ├── forms.css         # Formulär
│   │   │   ├── cards.css         # Kort
│   │   │   ├── navigation.css    # Navigation
│   │   │   ├── modals.css        # Modaler
│   │   │   ├── alerts.css        # Alerts
│   │   │   ├── badges.css        # Badges
│   │   │   ├── tables.css        # Tabeller
│   │   │   ├── typography.css    # Typografi-komponenter
│   │   │   └── general.css       # Allmänna komponenter
│   │   └── utilities/            # Utility-klasser
│   │       ├── spacing.css       # Margin/padding utilities
│   │       ├── typography.css    # Text utilities
│   │       ├── colors.css        # Färg utilities
│   │       ├── layout.css        # Layout utilities
│   │       └── responsive.css    # Responsive utilities
│   ├── js/
│   │   └── main.js               # Huvudfil för JavaScript
│   └── php/
│       ├── theme-setup.php       # Grundläggande tema-funktioner
│       ├── theme-options.php     # ACF tema-inställningar
│       ├── woocommerce-setup.php # WooCommerce integration
│       ├── acf-blocks.php        # ACF block registrering
│       ├── lazy-loading.php      # Bildoptimering
│       └── security-headers.php  # Säkerhetsheaders
├── template-parts/
│   ├── header.php                # Header dispatcher
│   ├── footer.php                # Footer dispatcher
│   ├── headers-templates/        # Header-mallar
│   │   └── header-style1.php
│   ├── footers-templates/        # Footer-mallar
│   │   ├── footer-simple.php
│   │   ├── footer-corporate.php
│   │   ├── footer-newsletter.php
│   │   ├── footer-social.php
│   │   └── footer-contact.php
│   └── blocks/                   # ACF Blocks
│       ├── hero/
│       │   └── block-hero.php
│       └── text/
│           └── block-text.php
├── woocommerce/                  # WooCommerce templates
│   ├── single-product.php        # Produktsida dispatcher
│   ├── archive-product.php       # Produktarkiv dispatcher
│   ├── cart/
│   │   └── cart.php              # Varukorg dispatcher
│   ├── checkout/
│   │   └── form-checkout.php     # Checkout dispatcher
│   ├── myaccount/
│   │   └── my-account.php        # Mitt konto dispatcher
│   └── auth/
│       ├── form-login.php        # Login dispatcher
│       └── form-register.php     # Registrering dispatcher
├── template-parts/woocommerce-templates/ # WooCommerce mallar
│   ├── single-product/
│   │   └── single-product-style1.php
│   ├── archive-product/
│   │   └── archive-product-style1.php
│   ├── cart/
│   │   └── cart-style1.php
│   ├── checkout/
│   │   └── checkout-style1.php
│   ├── myaccount/
│   │   └── myaccount-style1.php
│   └── auth/
│       ├── login-style1.php
│       └── register-style1.php
├── functions.php                 # Huvudfunktioner
├── header.php                    # Huvud header-fil
├── footer.php                    # Huvud footer-fil
├── index.php                     # Flexible content loop
├── style.css                     # Tema-information
├── postcss.config.js             # PostCSS konfiguration
└── package.json                  # NPM dependencies
```

## ⚙️ Konfiguration

### Smort CSS Library

Temat använder ett eget CSS-bibliotek organiserat i moduler:

- **Foundations**: Grundläggande CSS-variabler och reset
- **Layout**: Container, grid och flexbox-system
- **Components**: Återanvändbara UI-komponenter
- **Utilities**: Utility-klasser för snabb styling

### ACF Theme Options

Tillgängliga inställningar via **WordPress Admin → Tema**:

- **Layout**: Header/footer styles
- **Färger**: Primary, secondary, accent, text, background
- **Typografi**: Anpassade fonter och storlekar
- **Logotyp**: Header och footer logotyper
- **Kontakt**: Företagsinformation och öppettider
- **Sociala medier**: Länkar till sociala plattformar
- **WooCommerce**: Layout-val för alla e-handelssidor

### CSS-variabler

Temat använder CSS-variabler från ACF:

```css
:root {
  --primary-color: #000000;
  --secondary-color: #ffffff;
  --accent-color: #b6b09f;
  --text-color: #333333;
  --background-color: #ffffff;

  /* Typografi */
  --heading-font: "Prompt", sans-serif;
  --body-font: "Prompt", sans-serif;

  /* Responsiva storlekar */
  --h1-lg-size: 3.5rem;
  --h1-sm-size: 2.25rem;
  /* ... etc */
}
```

## 🔧 Utveckling

### Skapa nya blocks

1. Skapa mapp: `template-parts/blocks/ditt-block/`
2. Skapa fil: `block-ditt-block.php`
3. Registrera i `assets/php/acf-blocks.php`

### CSS Workflow

1. Skriv CSS i relevanta moduler under `assets/css/`
2. Importera nya filer i `main.css` eller `components.css`
3. CSS kompileras automatiskt med PostCSS

### WooCommerce Templates

Alla WooCommerce-sidor använder dispatcher-systemet:

1. Huvudfil (t.ex. `woocommerce/single-product.php`) hämtar style från ACF
2. Laddar rätt template från `template-parts/woocommerce-templates/`
3. Lägg till nya styles genom att skapa nya template-filer

### Lazy Loading

Använd helper-funktionen för ACF-bilder:

```php
<?= get_lazy_image($image, 'full', 'w-full h-full object-cover') ?>
```

### Browser-sync Features

- **Live reload** - Automatisk uppdatering vid filändringar
- **Cross-device testing** - Testa på flera enheter samtidigt
- **CSS injection** - Snabb CSS uppdatering utan page reload

## 📦 Build för Production

### CSS Minifiering

```bash
npx postcss assets/css/main.css -o assets/css/main.min.css --env production
```

### Säkerhet

Temat inkluderar automatiskt:

- Säkerhetsheaders (X-Frame-Options, CSP, etc.)
- Sanitized output för alla ACF-fält
- Secure file uploads för typsnitt

## 🎨 CSS Library Features

### Utility Classes

```css
/* Spacing */
.p-4 {
  padding: 1rem;
}
.m-2 {
  margin: 0.5rem;
}

/* Typography */
.text-lg {
  font-size: var(--text-lg);
}
.font-bold {
  font-weight: 700;
}

/* Colors */
.text-primary {
  color: var(--primary-color);
}
.bg-secondary {
  background-color: var(--secondary-color);
}

/* Layout */
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.container {
  /* Responsive container */
}
```

### Component Classes

```css
/* Buttons */
.btn {
  /* Base button styles */
}
.btn-primary {
  /* Primary button */
}

/* Cards */
.card {
  /* Card component */
}

/* Forms */
.form-input {
  /* Input styling */
}
```

## 🛒 WooCommerce Integration

Temat inkluderar komplett WooCommerce-support med:

- **Anpassade templates** för alla sidor
- **Responsiv design** för mobil e-handel
- **ACF-integration** för layout-val
- **Optimerad checkout** process
- **Mitt konto** funktionalitet
- **Produktarkiv** med filter

## 📱 Responsive Design

- **Mobile-first** approach
- **Custom breakpoints**: 640px, 768px, 1024px, 1280px, 1536px
- **Responsive typography** med separata mobil/desktop storlekar
- **Flexibel container** som anpassar sig till skärmstorlek

## 🔧 Anpassning

### Lägg till nya färger

1. Uppdatera `assets/css/foundations/colors.css`
2. Lägg till i ACF theme options
3. Använd i CSS med `var(--din-nya-farg)`

### Skapa nya komponenter

1. Skapa fil i `assets/css/components/`
2. Importera i `main.css`
3. Använd CSS-variabler för konsistens

### Anpassa typografi

1. Ladda upp typsnitt via ACF
2. Uppdatera storlekar i tema-inställningar
3. CSS-variabler uppdateras automatiskt

## 📞 Support

För support och frågor, kontakta Smort AB eller skapa en issue i projektets repository.
