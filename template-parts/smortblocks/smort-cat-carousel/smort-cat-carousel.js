document.addEventListener("DOMContentLoaded", function () {
  var swiper = new Swiper(".swiper-container-kategori", {
    slidesPerView: 1,
    spaceBetween: 10,
    slidesPerGroup: 1,
    centeredSlides: false,
    initialSlide: 0,
    loop: true,
    loopAdditionalSlides: 2,
    loopFillGroupWithBlank: true,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    breakpoints: {
      320: {
        slidesPerView: 1.2,
        slidesPerGroup: 1,
        spaceBetween: 10,
      },
      480: {
        slidesPerView: 1.2,
        slidesPerGroup: 1,
        spaceBetween: 10,
      },
      768: {
        slidesPerView: 2,
        slidesPerGroup: 1,
        spaceBetween: 10,
      },
      1024: {
        slidesPerView: 3.2,
        slidesPerGroup: 1,
        spaceBetween: 20,
      },
    },
  });
});
