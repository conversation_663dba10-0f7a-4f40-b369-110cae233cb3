<?php

// Register sitebuilder field group för alla sidor och inlägg
function register_sitebuilder_field_group()
{

    // Hämta alla ACF Blocks för att lägga till i sitebuilder
    $acf_blocks = [];
    $excluded_blocks = ['theme_settings', 'sitebuilder'];
    if (function_exists('acf_add_local_field_group')) {
        $all_acf_blocks = acf_get_field_groups();

        foreach ($all_acf_blocks as $block) {
            if ($block['location'][0][0]['param'] !== 'block') {
                continue;
            }
            if (isset($block['name']) && in_array($block['name'], $excluded_blocks)) {
                continue;
            }
            $block_name = $block['name'] ?? 'fallback_name';
            $acf_blocks[$block_name] = $block;
        }
    }

    // Skapa Sitebuilder field group
    if (function_exists('acf_add_local_field_group')) {
        $layouts = array();

        foreach ($acf_blocks as $block) {
            $block_fields = acf_get_fields($block['key']);

            // Create a separate field group for each block
            $layouts[$block['name']] = array(
                'key' => 'layout_' . $block['name'],
                'name' => $block['name'],
                'label' => $block['title'],
                'display' => 'block',
                'sub_fields' => array(
                    array(
                        'key' => 'field_' . $block['name'] . '_content',
                        'label' => $block['title'] . ' Content',
                        'name' => $block['name'] . '_content',
                        'type' => 'group',
                        'sub_fields' => $block_fields
                    )
                )
            );
        }

        acf_add_local_field_group(array(
            'key' => 'group_sitebuilder',
            'title' => 'Sitebuilder',
            'name' => 'sitebuilder',
            'fields' => array(
                array(
                    'key' => 'field_sitebuilder',
                    'label' => 'Sitebuilder',
                    'name' => 'sitebuilder',
                    'type' => 'flexible_content',
                    'layouts' => $layouts
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'page',
                    ),
                ),
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'post',
                    ),
                ),
            ),
        ));
    }
}
add_action('acf/init', 'register_sitebuilder_field_group');

// Dölj standard editor för pages och posts
function hide_editor_for_pages()
{
    remove_post_type_support('page', 'editor');
    remove_post_type_support('post', 'editor');
}
add_action('init', 'hide_editor_for_pages');
