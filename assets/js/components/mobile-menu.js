// ==========================================================================
// MOBILE MENU COMPONENT
// ==========================================================================

export const MobileMenu = {
  // Cache DOM elements
  elements: {
    burgerButtons: null,
    mobileMenus: null,
    closeButtons: null,
    backdrops: null,
  },

  // Initialize component
  init() {
    this.cacheElements();
    this.bindEvents();
  },

  // Cache all DOM elements
  cacheElements() {
    this.elements.burgerButtons = document.querySelectorAll(".navbar-burger");
    this.elements.mobileMenus = document.querySelectorAll(".navbar-menu");
    this.elements.closeButtons = document.querySelectorAll(".navbar-close");
    this.elements.backdrops = document.querySelectorAll(".navbar-backdrop");
  },

  // Bind all event listeners
  bindEvents() {
    // Open menu events
    this.elements.burgerButtons.forEach((button) => {
      button.addEventListener("click", () => this.openMenu());
    });

    // Close menu events
    this.elements.closeButtons.forEach((button) => {
      button.addEventListener("click", () => this.closeMenu());
    });

    this.elements.backdrops.forEach((backdrop) => {
      backdrop.addEventListener("click", () => this.closeMenu());
    });

    // Escape key to close
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape") {
        this.closeMenu();
      }
    });
  },

  // Open mobile menu
  openMenu() {
    this.elements.mobileMenus.forEach((menu) => {
      menu.classList.remove("hidden");
    });
    this.toggleBodyScroll(true);
  },

  // Close mobile menu
  closeMenu() {
    this.elements.mobileMenus.forEach((menu) => {
      menu.classList.add("hidden");
    });
    this.toggleBodyScroll(false);
  },

  // Toggle body scroll
  toggleBodyScroll(disable) {
    document.body.style.overflow = disable ? "hidden" : "";
  },
};
