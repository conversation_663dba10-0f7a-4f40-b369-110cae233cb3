<?php
/*
Template Name: <PERSON><PERSON><PERSON>
*/

// Enqueue Swiper JS and CSS
wp_enqueue_script('swiper-js', 'https://unpkg.com/swiper/swiper-bundle.min.js', [], null, true);
wp_enqueue_style('swiper-css', 'https://unpkg.com/swiper/swiper-bundle.min.css');
// Prefix for CSS classes
$prefix = 'smort-cat-carousel';
// Create id attribute allowing for custom "anchor" value.
$block = $block ?? [];
if (!empty($block['id'])) {
  $id = $prefix . '-' . $block['id'];
} else {
  $id = $prefix;
}
if (!empty($block['anchor'])) {
  $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values
$className = $prefix;
if (!empty($block['className'])) {
  $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
  $className .= ' align' . $block['align'];
}

// Load values and assign defaults.
// When used in flexible content, fields are nested under the block name + '_content' group
$layout_name = get_row_layout();

// Try to access fields from the nested group structure first, then fallback to direct access
$title = get_sub_field('title') ?: 'Våra kategorier';
$selected_categories = get_sub_field('product_categories') ?: null;

// Check if custom repeater items exist first, otherwise use selected categories
$has_custom_items = have_rows('kategoriobjekt');

if ($has_custom_items || !empty($selected_categories)) : ?>
  <div class="<?= $prefix ?>-container">
    <div class="<?= $prefix ?>-swiper-container">
      <div class="<?= $prefix ?>-swiper">
        <?php if ($has_custom_items) : ?>
          <?php while (have_rows('kategoriobjekt')) : the_row();
            // Hämta värden från repeaterfältets subfält
            $background_image = get_sub_field('bakgrundsbild');
            $title = get_sub_field('rubrik');
            $short_text = get_sub_field('kort_text');
            $link = get_sub_field('lank');
          ?>
            <!-- Skapa ett karusellobjekt från repeater -->
            <a href="<?php echo esc_url($link); ?>" class="swiper-slide kategori-item" style="background-image: url('<?php echo esc_url($background_image); ?>');">
              <div class="overlay"></div>
              <div class="kategori-info">
                <h2 class="kategori-title"><?php echo esc_html($title); ?></h2>
                <p class="kategori-text"><?php echo esc_html($short_text); ?></p>
              </div>
            </a>
          <?php endwhile; ?>
        <?php else : ?>
          <?php
          // Convert single category to array if needed
          if (!is_array($selected_categories)) {
            $selected_categories = array($selected_categories);
          }
          foreach ($selected_categories as $category) :
            // Get category data
            $category_link = get_term_link($category);
            $category_name = $category->name;
            $category_description = $category->description;

            // Get category thumbnail if available
            $thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
            $background_image = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '';
          ?>
            <!-- Skapa ett karusellobjekt från kategorier -->
            <a href="<?php echo esc_url($category_link); ?>" class="swiper-slide kategori-item" style="background-image: url('<?php echo esc_url($background_image); ?>');">
              <div class="overlay"></div>
              <div class="kategori-info">
                <h2 class="kategori-title"><?php echo esc_html($category_name); ?></h2>
                <p class="kategori-text"><?php echo esc_html($category_description); ?></p>
              </div>
            </a>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
  <div class="pagination-wrapper">
    <div class="swiper-button-prev"></div>
    <div class="swiper-button-next"></div>
  </div>
<?php else : ?>
  <!-- <p>Inga kategorier hittades.</p> -->
<?php endif; ?>