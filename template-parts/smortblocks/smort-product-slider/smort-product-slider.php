<?php

/**
 * Sharper Product Slider Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during backend preview render.
 * @param   int $post_id The post ID the block is rendering content against.
 */

// Prefix for CSS classes
$prefix = 'smort-product-slider';
// Create id attribute allowing for custom "anchor" value.
$block = $block ?? [];
if (!empty($block['id'])) {
  $id = $prefix . '-' . $block['id'];
} else {
  $id = $prefix;
}
if (!empty($block['anchor'])) {
  $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values. 
$className = $prefix;
if (!empty($block['className'])) {
  $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
  $className .= ' align' . $block['align'];
}

// Load values and assign defaults.
$title = get_field('title') ?: 'Våra produkter';
$button_text = get_field('button_text') ?: 'Till produkten';

// Hämta valda produkter
$selected_products = get_field('selected_products');

// Om inga produkter är valda, använd fallback till kategorifiltrering
if (empty($selected_products)) {
  $product_count = get_field('product_count') ?: 8;
  $product_categories = get_field('product_categories') ?: array();

  // Hämta produkter från WooCommerce
  $args = array(
    'post_type'      => 'product',
    'posts_per_page' => $product_count,
    'post_status'    => 'publish',
  );

  // Lägg till kategorifilter om det är valt
  if (!empty($product_categories)) {
    $args['tax_query'] = array(
      array(
        'taxonomy' => 'product_cat',
        'field'    => 'term_id',
        'terms'    => $product_categories,
        'operator' => 'IN',
      ),
    );
  }

  $products_query = new WP_Query($args);
  wp_reset_query();
} else {
  // Använd de specifikt valda produkterna
  $args = array(
    'post_type'      => 'product',
    'post__in'       => $selected_products,
    'posts_per_page' => -1,
    'orderby'        => 'post__in', // Behåll ordningen från ACF-fältet
    'post_status'    => 'publish',
  );

  $products_query = new WP_Query($args);
  wp_reset_query();
}

// Get smort-product-slider.css styling
wp_enqueue_style('smort-product-slider-style', get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.css', array(), '1.0', 'all');


?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
  <?php /* Titeln tas bort enligt önskemål */ ?>

  <?php if ($products_query->have_posts()) : ?>
    <!-- Navigation arrows -->
    <div class="<?= $prefix ?>-nav">
      <button class="<?= $prefix ?>-nav-prev" aria-label="Föregående">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M15 18l-6-6 6-6" />
        </svg>
      </button>
      <button class="<?= $prefix ?>-nav-next" aria-label="Nästa">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 18l6-6-6-6" />
        </svg>
      </button>
    </div>
    <div class="<?= $prefix ?>-container">
      <div class="<?= $prefix ?>-track">
        <?php
        $count = 0;
        while ($products_query->have_posts()) : $products_query->the_post();
          global $product;

          // Kontrollera att det är en giltig WooCommerce-produkt
          if (!is_a($product, 'WC_Product')) {
            continue;
          }
          $count++;
          $product_id = $product->get_id();
        ?>
          <?php _sc('product-item', [
            'product_id' => $product_id,
          ]) ?>

        <?php endwhile; ?>
        <?php wp_reset_postdata(); ?>
      </div>

      <!-- Progress bar -->
      <div class="<?= $prefix ?>-progress-container">
        <div class="<?= $prefix ?>-progress-bar"></div>
      </div>
    </div>
  <?php elseif (is_admin()) : ?>
    <div class="<?= $prefix ?>-placeholder">
      <p><?php _e('Inga produkter hittades. Välj specifika produkter eller ändra filterinställningarna.', 'sharper'); ?></p>
    </div>
  <?php endif; ?>
</div>