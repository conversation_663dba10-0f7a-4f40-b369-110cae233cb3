/* Smort Product Slider Styles - Uppdaterad enligt referensbild */

.smort-product-slider {
  position: relative;
  width: 100%;
  margin: var(--space-10) 0;
  padding: 0;
  background-color: transparent;
  overflow: visible; /* Säkerställer att pilarna syns även om de går utanför */
}

.smort-product-slider-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 30px;
  text-transform: uppercase;
}

/* Slider container */
.smort-product-slider-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding-bottom: 15px; /* Utrymme för progress bar */
}

/* Navigation arrows */
.smort-product-slider-nav {
  position: relative;
  display: flex;
  gap: 10px;
  z-index: 20;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.smort-product-slider-nav-prev,
.smort-product-slider-nav-next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: transparent;
  border: 1px solid var(--color-border);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color);
  padding: 0;
}

.smort-product-slider-nav-prev:hover,
.smort-product-slider-nav-next:hover {
  background-color: #f5f5f5;
  color: var(--text-color);
}

.smort-product-slider-nav-prev:focus,
.smort-product-slider-nav-next:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Slider track */
.smort-product-slider-track {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  cursor: grab;
  padding-bottom: 10px;
  position: relative;
}

.smort-product-slider-track:active {
  cursor: grabbing;
}

/* Dölj scrollbar */
.smort-product-slider-track::-webkit-scrollbar {
  display: none;
}

/* Produkt-item */
.smort-product-slider-item {
  flex: 0 0 calc(25% - 7.5px); /* 4 produkter i desktop med 10px gap */
  background-color: transparent;
  border: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  scroll-snap-align: start;
  min-width: 0; /* Förhindrar att innehållet expanderar utanför containern */
}

.smort-product-slider-item-link {
  display: block;
  width: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 1;
}

/* Produkt-bild container */
.smort-product-slider-item-image-container {
  position: relative;
  width: 100%;
  padding-bottom: 115%; /* Ökad höjd enligt önskemål */
  background-color: transparent;
  overflow: hidden;
  margin-bottom: 10px;
}

/* Produkt-bild */
.smort-product-slider-item-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ändrat till cover enligt önskemål */
  padding: 0;
  transition: transform 0.3s ease;
  cursor: pointer;
}

/* Knapp-container */
.smort-product-slider-item-button-container {
  margin-top: 10px;
  height: 40px; /* Fast höjd för att undvika hopp */
}

/* Produkt-info */
.smort-product-slider-item-info {
  padding: 15px 0 5px 0;
  display: flex;
  flex-direction: column;
}

/* Produktnamn */
.smort-product-slider-item-name {
  font-size: 11px;
  font-weight: 600;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  color: #000;
  height: 32px; /* Fast höjd för 2-3 rader text */
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Pris och lager-container */
.smort-product-slider-item-price-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Pris */
.smort-product-slider-item-price {
  font-size: 14px;
  font-weight: 400;
  color: #000;
}

/* Lager-status */
.smort-product-slider-item-stock {
  font-size: 14px;
  text-transform: uppercase;
  text-align: right;
  color: #000;
}

.smort-product-slider-item-stock.in-stock {
  color: var(--color-success);
}

.smort-product-slider-item-stock.on-backorder{
  color: var(--color-warning);
}

.smort-product-slider-item-stock.out-of-stock {
  color: var(--color-error);
}

/* Progress bar */
.smort-product-slider-progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--secondary-color)
  z-index: 10;
}

.smort-product-slider-progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: #000;
  transition: width 0.3s ease;
}

/* Admin placeholder */
.smort-product-slider-placeholder {
  padding: 40px;
  background: #f0f0f0;
  text-align: center;
  border: 1px dashed var(--color-border);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .smort-product-slider-item {
    flex: 0 0 calc(33.333% - 7px); /* 3 produkter i mindre desktop */
  }
}

@media (max-width: 768px) {
  .smort-product-slider-track {
    gap: 10px;
  }

  .smort-product-slider-item {
    flex: 0 0 calc(33.333% - 7px); /* 3 produkter i tablet */
    height: auto;
    scroll-snap-align: start;
  }

  .smort-product-slider-nav {
    margin-bottom: 10px;
  }

  .smort-product-slider-nav-prev,
  .smort-product-slider-nav-next {
    width: 28px;
    height: 28px;
  }

  .smort-product-slider-link {
    display: block;
    width: 100%;
    text-decoration: none;
    pointer-events: auto;
  }

  .smort-product-slider-title {
    font-size: 28px;
    margin-bottom: 20px;
  }

  .smort-product-slider-image-container {
    padding-bottom: 100%;
    height: 0;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .smort-product-slider-track {
    gap: 10px;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
    scroll-snap-type: x mandatory;
  }

  .smort-product-slider-item {
    flex: 0 0 calc(50% - 5px); /* 2 produkter i mobil */
    height: auto;
    margin: 0;
    display: flex;
    flex-direction: column;
    scroll-snap-align: start;
  }

  .smort-product-slider-nav {
    margin-bottom: 8px;
  }

  .smort-product-slider-nav-prev,
  .smort-product-slider-nav-next {
    width: 24px;
    height: 24px;
  }

  .smort-product-slider-nav-prev svg,
  .smort-product-slider-nav-next svg {
    width: 14px;
    height: 14px;
  }

  .smort-product-slider-link {
    display: block;
    width: 100%;
    text-decoration: none;
    pointer-events: auto;
  }

  .smort-product-slider-name {
    font-size: 10px !important;
    height: 35px; /* Något mindre höjd i mobil */
    line-height: 1.4;
  }

  .smort-product-slider-price {
    font-size: 11px;
  }

  .smort-product-slider-stock {
    font-size: 9px !important;
  }

  .smort-product-slider-button {
    padding: 6px 12px;
    font-size: 14px;
  }

  .smort-product-slider-title {
    font-size: 24px;
    margin-bottom: 15px;
  }

  .smort-product-slider-image-container {
    padding-bottom: 100%;
    height: 0;
    width: 100%;
    margin-bottom: 10px;
  }

  /* Visa knappen hela tiden i mobil istället för bara vid hover */
  .smort-product-slider-button-container {
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Behåll overflow för att kunna scrolla */
  .smort-product-slider-container {
    overflow: hidden;
    padding-bottom: 30px;
  }

  .smort-product-slider-progress-container {
    bottom: 0;
  }
}