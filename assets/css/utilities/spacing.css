/* ==========================================================================
   SPACING VARIABLES
   ========================================================================== */

:root {
  /* SPACING */
  --space-0: 0;
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem; /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem; /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem; /* 24px */
  --space-8: 2rem; /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem; /* 48px */
  --space-16: 4rem; /* 64px */
  --space-20: 5rem; /* 80px */
  --space-24: 6rem; /* 96px */
  --space-32: 8rem; /* 128px */
}

/* ==========================================================================
   SPACING UTILITIES
   ========================================================================== */

/* Padding */
.p-0 {
  padding: var(--space-0);
}
.p-1 {
  padding: var(--space-1);
}
.p-2 {
  padding: var(--space-2);
}
.p-3 {
  padding: var(--space-3);
}
.p-4 {
  padding: var(--space-4);
}
.p-5 {
  padding: var(--space-5);
}
.p-6 {
  padding: var(--space-6);
}
.p-8 {
  padding: var(--space-8);
}
.p-10 {
  padding: var(--space-10);
}
.p-12 {
  padding: var(--space-12);
}
.p-16 {
  padding: var(--space-16);
}
.p-20 {
  padding: var(--space-20);
}
.p-24 {
  padding: var(--space-24);
}
.p-32 {
  padding: var(--space-32);
}

/* Padding X & Y */
.px-0 {
  padding-left: var(--space-0);
  padding-right: var(--space-0);
}
.px-1 {
  padding-left: var(--space-1);
  padding-right: var(--space-1);
}
.px-2 {
  padding-left: var(--space-2);
  padding-right: var(--space-2);
}
.px-3 {
  padding-left: var(--space-3);
  padding-right: var(--space-3);
}
.px-4 {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}
.px-6 {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

.py-0 {
  padding-top: var(--space-0);
  padding-bottom: var(--space-0);
}
.py-1 {
  padding-top: var(--space-1);
  padding-bottom: var(--space-1);
}
.py-2 {
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
}
.py-3 {
  padding-top: var(--space-3);
  padding-bottom: var(--space-3);
}
.py-4 {
  padding-top: var(--space-4);
  padding-bottom: var(--space-4);
}
.py-6 {
  padding-top: var(--space-6);
  padding-bottom: var(--space-6);
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

/* Margin */
.m-0 {
  margin: var(--space-0);
}
.m-1 {
  margin: var(--space-1);
}
.m-2 {
  margin: var(--space-2);
}
.m-3 {
  margin: var(--space-3);
}
.m-4 {
  margin: var(--space-4);
}
.m-6 {
  margin: var(--space-6);
}
.m-8 {
  margin: var(--space-8);
}

/* Margin X & Y */
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0 {
  margin-top: var(--space-0);
  margin-bottom: var(--space-0);
}
.my-2 {
  margin-top: var(--space-2);
  margin-bottom: var(--space-2);
}
.my-4 {
  margin-top: var(--space-4);
  margin-bottom: var(--space-4);
}
.my-6 {
  margin-top: var(--space-6);
  margin-bottom: var(--space-6);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.mb-2 {
  margin-bottom: var(--space-2);
}
.mb-4 {
  margin-bottom: var(--space-4);
}
.mb-6 {
  margin-bottom: var(--space-6);
}
.mb-8 {
  margin-bottom: var(--space-8);
}

.mt-2 {
  margin-top: var(--space-2);
}
.mt-4 {
  margin-top: var(--space-4);
}
.mt-6 {
  margin-top: var(--space-6);
}
.mt-8 {
  margin-top: var(--space-8);
}

/* Responsive Spacing */
@media (min-width: 768px) {
  .md\:m-0 {
    margin: var(--space-0);
  }
  .md\:m-4 {
    margin: var(--space-4);
  }
  .md\:m-8 {
    margin: var(--space-8);
  }
  .md\:p-0 {
    padding: var(--space-0);
  }
  .md\:p-4 {
    padding: var(--space-4);
  }
  .md\:p-8 {
    padding: var(--space-8);
  }
  .md\:px-8 {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
  .md\:py-8 {
    padding-top: var(--space-8);
    padding-bottom: var(--space-8);
  }
}

@media (min-width: 1024px) {
  .lg\:m-0 {
    margin: var(--space-0);
  }
  .lg\:m-8 {
    margin: var(--space-8);
  }
  .lg\:m-16 {
    margin: var(--space-16);
  }
  .lg\:p-0 {
    padding: var(--space-0);
  }
  .lg\:p-8 {
    padding: var(--space-8);
  }
  .lg\:p-16 {
    padding: var(--space-16);
  }
  .lg\:px-16 {
    padding-left: var(--space-16);
    padding-right: var(--space-16);
  }
  .lg\:py-16 {
    padding-top: var(--space-16);
    padding-bottom: var(--space-16);
  }
}
