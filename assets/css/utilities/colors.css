/* ==========================================================================
   COLOR VARIABLES
   ========================================================================== */

:root {
  /* THEME COLORS - Synced with ACF Theme Options */
  --primary-color: #000000;
  --secondary-color: #ffffff;
  --accent-color: #b6b09f;
  --text-color: #333333;
  --background-color: #ffffff;

  /* EXTENDED COLOR PALETTE */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  --color-text-light: #6b7280;
  --color-text-lighter: #9ca3af;

  --color-background-alt: #f9fafb;
  --color-background-dark: #f3f4f6;

  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-border-dark: #d1d5db;
}
/* ==========================================================================
   COLOR UTILITIES
   ========================================================================== */

/* Background Colors - Theme */
.bg-primary {
  background-color: var(--primary-color);
}
.bg-secondary {
  background-color: var(--secondary-color);
}
.bg-accent {
  background-color: var(--accent-color);
}
.bg-background {
  background-color: var(--background-color);
}

/* Background Colors - Extended */
.bg-success {
  background-color: var(--color-success);
}
.bg-warning {
  background-color: var(--color-warning);
}
.bg-error {
  background-color: var(--color-error);
}
.bg-info {
  background-color: var(--color-info);
}

.bg-background-alt {
  background-color: var(--color-background-alt);
}
.bg-background-dark {
  background-color: var(--color-background-dark);
}

/* Background Colors - Standard */
.bg-white {
  background-color: #ffffff;
}
.bg-black {
  background-color: #000000;
}
.bg-gray-50 {
  background-color: #f9fafb;
}
.bg-gray-100 {
  background-color: #f3f4f6;
}
.bg-gray-800 {
  background-color: #1f2937;
}
.bg-gray-900 {
  background-color: #111827;
}

/* Text Colors - Theme */
.text-primary {
  color: var(--primary-color);
}
.text-secondary {
  color: var(--secondary-color);
}
.text-accent {
  color: var(--accent-color);
}
.text-color {
  color: var(--text-color);
}

/* Text Colors - Extended */
.text-success {
  color: var(--color-success);
}
.text-warning {
  color: var(--color-warning);
}
.text-error {
  color: var(--color-error);
}
.text-info {
  color: var(--color-info);
}

.text-light {
  color: var(--color-text-light);
}
.text-lighter {
  color: var(--color-text-lighter);
}

/* Text Colors - Standard */
.text-white {
  color: #ffffff;
}
.text-black {
  color: #000000;
}
.text-gray-300 {
  color: #d1d5db;
}
.text-gray-600 {
  color: #4b5563;
}
.text-gray-800 {
  color: #1f2937;
}

/* Hover Colors */
.hover\:text-primary:hover {
  color: var(--primary-color);
}
.hover\:text-accent:hover {
  color: var(--accent-color);
}
.hover\:text-info:hover {
  color: var(--color-info);
}
.hover\:bg-primary:hover {
  background-color: var(--primary-color);
}

/* Border Colors */
.border-primary {
  border-color: var(--primary-color);
}
.border-color {
  border-color: var(--color-border);
}
.border-light {
  border-color: var(--color-border-light);
}
.border-dark {
  border-color: var(--color-border-dark);
}
