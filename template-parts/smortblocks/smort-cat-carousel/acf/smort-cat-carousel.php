<?php

/**
 * ACF-f<PERSON>lt för Smort Cat Carousel block
 */

if (function_exists('acf_add_local_field_group')) {
  acf_add_local_field_group(array(
    'key' => 'group_smort_cat_carousel_fields',
    'title' => 'Smort Cat Carousel',
    'name' => 'smort-cat-carousel',
    'fields' => array(
      array(
        'key' => 'field_smort_cat_carousel_text',
        'label' => 'Title',
        'name' => 'title',
        'type' => 'text',
        'instructions' => 'Enter the text for your block',
        'required' => 0,
        'conditional_logic' => 0,
        'wrapper' => array(
          'width' => '',
          'class' => '',
          'id' => '',
        ),
        'default_value' => '',
        'placeholder' => 'Enter text...',
        'prepend' => '',
        'append' => '',
        'maxlength' => '',
      ),
      array(
        'key' => 'field_smort_cat_carousel_categories',
        'label' => 'Product Categories',
        'name' => 'product_categories',
        'type' => 'taxonomy',
        'instructions' => 'Select product categories to display automatically',
        'required' => 0,
        'conditional_logic' => 0,
        'taxonomy' => 'product_cat', // WooCommerce product categories
        'field_type' => 'multi_select', // or 'checkbox' or 'select'
        'allow_null' => 1,
        'add_term' => 0, // prevent adding new terms here
        'save_terms' => 0,
        'load_terms' => 0,
        'return_format' => 'object', // gives full term object with ID, name, slug, etc.
        'multiple' => 1,
      ),
      array(
        'key' => 'field_smort_cat_carousel_items',
        'label' => 'Kategori Objekt',
        'name' => 'kategoriobjekt',
        'type' => 'repeater',
        'instructions' => 'Lägg till anpassade kategoriobjekt (används istället för automatiska kategorier om ifyllt)',
        'required' => 0,
        'sub_fields' => array(
          array(
            'key' => 'field_cat_background_image',
            'label' => 'Bakgrundsbild',
            'name' => 'bakgrundsbild',
            'type' => 'image',
            'return_format' => 'url',
          ),
          array(
            'key' => 'field_cat_title',
            'label' => 'Rubrik',
            'name' => 'rubrik',
            'type' => 'text',
          ),
          array(
            'key' => 'field_cat_short_text',
            'label' => 'Kort text',
            'name' => 'kort_text',
            'type' => 'textarea',
            'rows' => 3,
          ),
          array(
            'key' => 'field_cat_link',
            'label' => 'Länk',
            'name' => 'lank',
            'type' => 'url',
          ),
        ),
        'min' => 0,
        'max' => 10,
        'layout' => 'table',
        'button_label' => 'Lägg till kategori',
      ),
    ),
    'location' => array(
      array(
        array(
          'param' => 'block',
          'operator' => '==',
          'value' => 'acf/smort-cat-carousel',
        ),
      ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => 'Fields for the Smort Cat Carousel block',
  ));
}
