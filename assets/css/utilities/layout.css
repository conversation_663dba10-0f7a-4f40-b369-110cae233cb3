/* ==========================================================================
   LAYOUT UTILITIES
   ========================================================================== */

/* Display & Block */
.block {
  display: block;
}
.text-center {
  text-align: center;
}

/* Mobile Menu Specific */
.navbar-menu.hidden {
  display: none;
}

.navbar-menu:not(.hidden) {
  display: block;
}

/* Flexbox */
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}

/* Gap */
.gap-1 {
  gap: var(--space-1);
}
.gap-2 {
  gap: var(--space-2);
}
.gap-3 {
  gap: var(--space-3);
}
.gap-4 {
  gap: var(--space-4);
}
.gap-6 {
  gap: var(--space-6);
}
.gap-8 {
  gap: var(--space-8);
}

/* Position */
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.sticky {
  position: sticky;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.top-0 {
  top: 0;
}
.right-0 {
  right: 0;
}
.bottom-0 {
  bottom: 0;
}
.left-0 {
  left: 0;
}

/* Z-index */
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: var(--z-modal);
}

/* Width & Height */
.w-full {
  width: 100%;
}
.w-auto {
  width: auto;
}
.w-6 {
  width: 1.5rem;
}
.w-12 {
  width: 3rem;
}
.w-5\/6 {
  width: 83.333333%;
}

.h-full {
  height: 100%;
}
.h-auto {
  height: auto;
}
.h-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.h-12 {
  height: 3rem;
}

.max-w-sm {
  max-width: 24rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-lg {
  max-width: 32rem;
}

/* Responsive Display */
@media (min-width: 1024px) {
  .lg\:flex {
    display: flex;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:block {
    display: block;
  }
}
