<main class="container mx-auto px-4 py-8 max-w-6xl">
    <h1 class="text-3xl font-bold mb-8">Varukorg</h1>
    
    <?php do_action('woocommerce_before_cart'); ?>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <form class="woocommerce-cart-form" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post">
                    <?php do_action('woocommerce_before_cart_table'); ?>
                    
                    <table class="shop_table shop_table_responsive cart woocommerce-cart-form__contents w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="product-thumbnail p-4">Produkt</th>
                                <th class="product-name p-4">Namn</th>
                                <th class="product-price p-4">Pris</th>
                                <th class="product-quantity p-4">Antal</th>
                                <th class="product-subtotal p-4">Totalt</th>
                                <th class="product-remove p-4"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php do_action('woocommerce_before_cart_contents'); ?>
                            
                            <?php
                            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                                $_product = apply_filters('woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key);
                                $product_id = apply_filters('woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key);
                                
                                if ($_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters('woocommerce_cart_item_visible', true, $cart_item, $cart_item_key)) {
                                    $product_permalink = apply_filters('woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink($cart_item) : '', $cart_item, $cart_item_key);
                                    ?>
                                    <tr class="woocommerce-cart-form__cart-item <?php echo esc_attr(apply_filters('woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key)); ?> border-b">
                                        <td class="product-thumbnail p-4">
                                            <?php
                                            $thumbnail = apply_filters('woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key);
                                            if (!$product_permalink) {
                                                echo $thumbnail;
                                            } else {
                                                printf('<a href="%s">%s</a>', esc_url($product_permalink), $thumbnail);
                                            }
                                            ?>
                                        </td>
                                        
                                        <td class="product-name p-4" data-title="<?php esc_attr_e('Product', 'woocommerce'); ?>">
                                            <?php
                                            if (!$product_permalink) {
                                                echo wp_kses_post(apply_filters('woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key) . '&nbsp;');
                                            } else {
                                                echo wp_kses_post(apply_filters('woocommerce_cart_item_name', sprintf('<a href="%s">%s</a>', esc_url($product_permalink), $_product->get_name()), $cart_item, $cart_item_key));
                                            }
                                            
                                            do_action('woocommerce_after_cart_item_name', $cart_item, $cart_item_key);
                                            echo wc_get_formatted_cart_item_data($cart_item);
                                            
                                            if ($_product->backorders_require_notification() && $_product->is_on_backorder($cart_item['quantity'])) {
                                                echo wp_kses_post(apply_filters('woocommerce_cart_item_backorder_notification', '<p class="backorder_notification">' . esc_html__('Available on backorder', 'woocommerce') . '</p>', $product_id));
                                            }
                                            ?>
                                        </td>
                                        
                                        <td class="product-price p-4" data-title="<?php esc_attr_e('Price', 'woocommerce'); ?>">
                                            <?php echo apply_filters('woocommerce_cart_item_price', WC()->cart->get_product_price($_product), $cart_item, $cart_item_key); ?>
                                        </td>
                                        
                                        <td class="product-quantity p-4" data-title="<?php esc_attr_e('Quantity', 'woocommerce'); ?>">
                                            <?php
                                            if ($_product->is_sold_individually()) {
                                                $product_quantity = sprintf('1 <input type="hidden" name="cart[%s][qty]" value="1" />', $cart_item_key);
                                            } else {
                                                $product_quantity = woocommerce_quantity_input(
                                                    array(
                                                        'input_name'   => "cart[{$cart_item_key}][qty]",
                                                        'input_value'  => $cart_item['quantity'],
                                                        'max_value'    => $_product->get_max_purchase_quantity(),
                                                        'min_value'    => '0',
                                                        'product_name' => $_product->get_name(),
                                                    ),
                                                    $_product,
                                                    false
                                                );
                                            }
                                            
                                            echo apply_filters('woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item);
                                            ?>
                                        </td>
                                        
                                        <td class="product-subtotal p-4" data-title="<?php esc_attr_e('Subtotal', 'woocommerce'); ?>">
                                            <?php echo apply_filters('woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal($_product, $cart_item['quantity']), $cart_item, $cart_item_key); ?>
                                        </td>
                                        
                                        <td class="product-remove p-4">
                                            <?php
                                            echo apply_filters(
                                                'woocommerce_cart_item_remove_link',
                                                sprintf(
                                                    '<a href="%s" class="remove text-red-500 hover:text-red-700" aria-label="%s" data-product_id="%s" data-product_sku="%s">&times;</a>',
                                                    esc_url(wc_get_cart_remove_url($cart_item_key)),
                                                    esc_html__('Remove this item', 'woocommerce'),
                                                    esc_attr($product_id),
                                                    esc_attr($_product->get_sku())
                                                ),
                                                $cart_item_key
                                            );
                                            ?>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                            ?>
                            
                            <?php do_action('woocommerce_cart_contents'); ?>
                            
                            <tr>
                                <td colspan="6" class="actions p-4 bg-gray-50">
                                    <div class="flex justify-between items-center">
                                        <input type="submit" class="btn btn-secondary" name="update_cart" value="<?php esc_attr_e('Update cart', 'woocommerce'); ?>" />
                                        <?php do_action('woocommerce_cart_actions'); ?>
                                        <?php wp_nonce_field('woocommerce-cart', 'woocommerce-cart-nonce'); ?>
                                    </div>
                                </td>
                            </tr>
                            
                            <?php do_action('woocommerce_after_cart_contents'); ?>
                        </tbody>
                    </table>
                    <?php do_action('woocommerce_after_cart_table'); ?>
                </form>
            </div>
        </div>
        
        <!-- Cart Totals -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm p-6 sticky top-8">
                <?php do_action('woocommerce_before_cart_collaterals'); ?>
                <div class="cart-collaterals">
                    <?php
                    do_action('woocommerce_cart_collaterals');
                    woocommerce_cart_totals();
                    ?>
                </div>
                <?php do_action('woocommerce_after_cart_collaterals'); ?>
            </div>
        </div>
    </div>
    
    <?php do_action('woocommerce_after_cart'); ?>
</main>