/* ==========================================================================
   TYPOGRAPHY UTILITIES
   ========================================================================== */


   /* ==========================================================================
   TYPOGRAPHY VARIABLES
   ========================================================================== */

:root {
  /* TYPOGRAPHY - Synced with ACF Theme Options */
  --heading-font: "Prompt", sans-serif;
  --body-font: "Prompt", sans-serif;
  --font-mono: "Fira Code", "Monaco", "Cascadia Code", monospace;
  --font-body: var(--body-font);
  --font-heading: var(--heading-font);

  /* RESPONSIVE TYPOGRAPHY SIZES - Synced with ACF */

  /* STANDARD FONT SIZES */
  --text-xs: 0.75rem; /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem; /* 16px */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem; /* 20px */
  --text-2xl: 1.5rem; /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem; /* 36px */
  --text-5xl: 3rem; /* 48px */
  --text-6xl: 3.75rem; /* 60px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
}

/* Font Sizes */
.text-xs {
  font-size: var(--text-xs);
}
.text-sm {
  font-size: var(--text-sm);
}
.text-base {
  font-size: var(--text-base);
}
.text-lg {
  font-size: var(--text-lg);
}
.text-xl {
  font-size: var(--text-xl);
}
.text-2xl {
  font-size: var(--text-2xl);
}
.text-3xl {
  font-size: var(--text-3xl);
}
.text-4xl {
  font-size: var(--text-4xl);
}
.text-5xl {
  font-size: var(--text-5xl);
}
.text-6xl {
  font-size: var(--text-6xl);
}

/* Font Weights */
.font-thin {
  font-weight: var(--font-thin);
}
.font-light {
  font-weight: var(--font-light);
}
.font-normal {
  font-weight: var(--font-normal);
}
.font-medium {
  font-weight: var(--font-medium);
}
.font-semibold {
  font-weight: var(--font-semibold);
}
.font-bold {
  font-weight: var(--font-bold);
}
.font-extrabold {
  font-weight: var(--font-extrabold);
}
.font-black {
  font-weight: var(--font-black);
}

/* Line Heights */
.leading-none {
  line-height: var(--leading-none);
}
.leading-tight {
  line-height: var(--leading-tight);
}
.leading-snug {
  line-height: var(--leading-snug);
}
.leading-normal {
  line-height: var(--leading-normal);
}
.leading-relaxed {
  line-height: var(--leading-relaxed);
}
.leading-loose {
  line-height: var(--leading-loose);
}

/* Font Families */
.font-heading {
  font-family: var(--heading-font);
}
.font-body {
  font-family: var(--body-font);
}
.font-mono {
  font-family: var(--font-mono);
}

/* Text Transform */
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}

/* Text Align */
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

/* Text Decoration */
.no-underline {
  text-decoration: none;
}
.underline {
  text-decoration: underline;
}
