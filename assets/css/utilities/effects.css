/* ==========================================================================
   EFFECTS VARIABLES
   ========================================================================== */

:root {
  /* BORDERS */
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;

  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  --border-radius: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
  --border-radius-full: 9999px;

  /* SHADOWS */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* TRANSITIONS */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;

  /* Z-INDEX */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
/* ==========================================================================
   EFFECTS UTILITIES
   ========================================================================== */

/* Border Radius */
.rounded-none {
  border-radius: var(--border-radius-none);
}
.rounded-sm {
  border-radius: var(--border-radius-sm);
}
.rounded {
  border-radius: var(--border-radius);
}
.rounded-md {
  border-radius: var(--border-radius-md);
}
.rounded-lg {
  border-radius: var(--border-radius-lg);
}
.rounded-xl {
  border-radius: var(--border-radius-xl);
}
.rounded-2xl {
  border-radius: var(--border-radius-2xl);
}
.rounded-3xl {
  border-radius: var(--border-radius-3xl);
}
.rounded-full {
  border-radius: var(--border-radius-full);
}

/* Border Width */
.border {
  border-width: var(--border-width);
}
.border-2 {
  border-width: var(--border-width-2);
}
.border-4 {
  border-width: var(--border-width-4);
}
.border-8 {
  border-width: var(--border-width-8);
}

/* Shadows */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}
.shadow {
  box-shadow: var(--shadow);
}
.shadow-md {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}
.shadow-xl {
  box-shadow: var(--shadow-xl);
}
.shadow-2xl {
  box-shadow: var(--shadow-2xl);
}
.shadow-inner {
  box-shadow: var(--shadow-inner);
}

/* Transitions */
.transition-fast {
  transition: var(--transition-fast);
}
.transition {
  transition: var(--transition-normal);
}
.transition-slow {
  transition: var(--transition-slow);
}

/* Z-Index */
.z-dropdown {
  z-index: var(--z-dropdown);
}
.z-sticky {
  z-index: var(--z-sticky);
}
.z-fixed {
  z-index: var(--z-fixed);
}
.z-modal-backdrop {
  z-index: var(--z-modal-backdrop);
}
.z-modal {
  z-index: var(--z-modal);
}
.z-popover {
  z-index: var(--z-popover);
}
.z-tooltip {
  z-index: var(--z-tooltip);
}
.z-toast {
  z-index: var(--z-toast);
}
