/**
 * Product Item Block Styles
 */

 /* Main Product Item container */
.product-item {
  /* Add your styles here */
  list-style: none;
  flex: 0 0 calc(20% - 7.5px); 

    .stock-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .stock-green .stock-dot {
    background-color: var(--color-success);
  }

  .stock-yellow .stock-dot {
    background-color: var(--color-warning);
  }

  .stock-red .stock-dot {
    background-color: var(--color-error);
  }
}

.product-item-card {
  span.onsale {
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
  }
}
/* Product Item Prop styles */
.product-item-primary {
  /* Add your styles here */
}

.product-item-secondary {
  /* Add your styles here */
}

.product-item-small {
  /* Add your styles here */
}

.product-item-medium {
  /* Add your styles here */
}

.product-item-large {
  /* Add your styles here */
}

/* Responsive styles */
@media (width <= 1024px) {
  /* Add your styles here */
    .product-item {
      /* Add your styles here */
    flex: 0 0 calc(25% - 7px);

    }
}
@media (width <= 768px) {
  /* Add your styles here */
    .product-item {
      /* Add your styles here */
    flex: 0 0 calc(33.333% - 7px);

    }
}
@media (width <= 480px) {
  /* Add your styles here */
    .product-item {
      /* Add your styles here */
      flex: 0 0 calc(50% - 7px);
    }
}

