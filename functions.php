<?php
// Base theme functions
include_once 'assets/php/theme-setup.php';
include_once 'assets/php/woocommerce-setup.php';
include_once 'assets/php/theme-options.php';
include_once 'assets/php/security-headers.php';

/* Register blocks */
function register_acf_block_types()
{
	acf_register_block_type(array(
		'name' => 'smort-product-slider',
		'title' => __("Smort - Smort Product Slider"),
		'description' => __('Smort block'),
		'render_template' => '/template-parts/smortblocks/smort-product-slider/smort-product-slider.php',
		'category' => 'smort',
		'icon' => 'id',
		'keywords' => array('CTA', 'Block'),
		'mode' => 'edit',
		'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.css',
		'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-product-slider/smort-product-slider.js'
	));
}

// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
	add_action('acf/init', 'register_acf_block_types');
}

/* Include ACF field groups */
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-product-slider/acf/smort-product-slider.php';





include_once 'assets/php/acf-blocks.php';
