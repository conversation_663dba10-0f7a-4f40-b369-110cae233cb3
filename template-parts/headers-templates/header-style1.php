<header class="bg-primary">
  <!-- Top Bar -->
  <div class="bg-gray-800 text-white py-2">
    <div class="container flex justify-between items-center text-sm">
      <div class="flex gap-4">
        <?php if (get_field('phone', 'option')): ?>
          <span>📞 <?= get_field('phone', 'option') ?></span>
        <?php endif; ?>
        <?php if (get_field('email', 'option')): ?>
          <span>✉️ <?= get_field('email', 'option') ?></span>
        <?php endif; ?>
      </div>
      <div class="flex gap-3">
        <?php if (get_field('facebook_url', 'option')): ?>
          <a href="<?= get_field('facebook_url', 'option') ?>" class="hover-text-blue">
            <i class="fab fa-facebook-f"></i>
          </a>
        <?php endif; ?>
        <?php if (get_field('linkedin_url', 'option')): ?>
          <a href="<?= get_field('linkedin_url', 'option') ?>" class="hover-text-blue">
            <i class="fab fa-linkedin-in"></i>
          </a>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <!-- Main Header -->
  <nav class="container py-6 flex justify-between items-center">
    <div class="flex items-center">
      <a href="<?php echo get_bloginfo('url'); ?>" class="text-2xl font-bold text-primary">
        <?php if (get_field('header_logo', 'option')): ?>
          <img class="h-12" src="<?= get_field('header_logo', 'option')['url'] ?>" alt="<?= get_bloginfo('name') ?>">
        <?php else: ?>
          <?php echo get_bloginfo('name'); ?>
        <?php endif; ?>
      </a>
    </div>

    <?php wp_nav_menu(array(
      'theme_location' => 'header-menu',
      'container' => false,
      'menu_class' => 'desktop-menu-container flex gap-8 text-black font-semibold uppercase'
    )); ?>

    <div class="desktop-menu-container flex items-center gap-4">
      <a href="/kontakt" class="btn btn-primary">Kontakt</a>
    </div>

    <button class="mobile-menu-toggle lg-hidden navbar-burger">
      <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
        <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"></path>
      </svg>
    </button>
  </nav>

  <!-- Mobile Menu -->
  <div class="navbar-menu hidden">
    <div class="navbar-backdrop fixed inset-0 bg-black opacity-50"></div>
    <nav class="fixed top-0 right-0 bottom-0 w-5/6 max-w-sm bg-white px-6 py-6 z-50">
      <div class="flex items-center justify-between mb-8">
        <span class="text-lg font-bold"><?php bloginfo('name'); ?></span>
        <button class="navbar-close">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <?php wp_nav_menu(array(
        'theme_location' => 'header-menu',
        'container' => false,
        'menu_class' => 'mb-8'
      )); ?>
      <a href="/kontakt" class="btn btn-primary block text-center">Kontakt</a>
    </nav>
  </div>
</header>