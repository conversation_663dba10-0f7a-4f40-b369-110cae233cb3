<?php

// WooCommerce theme support
function smort_tema_woocommerce_support()
{
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
}
add_action('after_setup_theme', 'smort_tema_woocommerce_support');

// Remove WooCommerce default styles
add_filter('woocommerce_enqueue_styles', '__return_empty_array');

// Customize WooCommerce wrapper
remove_action('woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10);
remove_action('woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10);

add_action('woocommerce_before_main_content', 'smort_tema_wrapper_start', 10);
add_action('woocommerce_after_main_content', 'smort_tema_wrapper_end', 10);

function smort_tema_wrapper_start()
{
    echo '<main class="container mx-auto px-4 py-8">';
}

function smort_tema_wrapper_end()
{
    echo '</main>';
}

// Customize product loop
function smort_tema_loop_columns()
{
    return 3; // 3 products per row
}
add_filter('loop_shop_columns', 'smort_tema_loop_columns');

// Change number of products per page
function smort_tema_products_per_page()
{
    return 12;
}
add_filter('loop_shop_per_page', 'smort_tema_products_per_page');

// Remove breadcrumbs
remove_action('woocommerce_before_main_content', 'woocommerce_breadcrumb', 20);

// Customize add to cart button text
function smort_tema_add_to_cart_text()
{
    return __('Lägg i varukorg');
}
add_filter('woocommerce_product_add_to_cart_text', 'smort_tema_add_to_cart_text');
