<?php

// ACF Options page för tema-inställningar
if (function_exists('acf_add_options_page')) {
    acf_add_options_page(array(
        'page_title' => 'Tema-inställningar',
        'menu_title' => 'Tema',
        'menu_slug' => 'theme-settings',
        'icon_url' => 'dashicons-admin-customizer'
    ));
}

// Register tema-inställningar fält
function register_theme_settings_fields()
{
    if (function_exists('acf_add_local_field_group')) {
        acf_add_local_field_group(array(
            'key' => 'group_theme_settings',
            'name' => 'theme_settings',
            'title' => 'Tema Inställningar',
            'fields' => array(

                // LAYOUT FLIK
                array(
                    'key' => 'field_layout_tab',
                    'label' => 'Layout',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_header_style',
                    'label' => 'Header Style',
                    'name' => 'header_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_footer_style',
                    'label' => 'Footer Style',
                    'name' => 'footer_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),

                // FÄRGER FLIK
                array(
                    'key' => 'field_colors_tab',
                    'label' => 'Färger',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_primary_color',
                    'label' => 'Primär Färg',
                    'name' => 'primary_color',
                    'type' => 'color_picker',
                    'default_value' => '#000000',
                ),
                array(
                    'key' => 'field_secondary_color',
                    'label' => 'Sekundär Färg',
                    'name' => 'secondary_color',
                    'type' => 'color_picker',
                    'default_value' => '#ffffff',
                ),
                array(
                    'key' => 'field_accent_color',
                    'label' => 'Accent Färg',
                    'name' => 'accent_color',
                    'type' => 'color_picker',
                    'default_value' => '#B6B09F',
                ),
                array(
                    'key' => 'field_text_color',
                    'label' => 'Text Färg',
                    'name' => 'text_color',
                    'type' => 'color_picker',
                    'default_value' => '#333333',
                ),
                array(
                    'key' => 'field_background_color',
                    'label' => 'Bakgrundsfärg',
                    'name' => 'background_color',
                    'type' => 'color_picker',
                    'default_value' => '#333333',
                ),

                // TYPOGRAFI FLIK
                array(
                    'key' => 'field_typography_tab',
                    'label' => 'Typografi',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_heading_font_file',
                    'label' => 'Rubrik Typsnitt (Ladda upp)',
                    'name' => 'heading_font_file',
                    'type' => 'file',
                    'instructions' => 'Ladda upp .woff2, .woff eller .ttf fil för rubriker',
                    'return_format' => 'array',
                    'library' => 'all',
                    'mime_types' => 'woff2,woff,ttf,otf',
                ),
                array(
                    'key' => 'field_heading_font_name',
                    'label' => 'Rubrik Typsnitt Namn',
                    'name' => 'heading_font_name',
                    'type' => 'text',
                    'instructions' => 'Ange namnet på typsnittet (t.ex. "Min Custom Font")',
                    'default_value' => 'Custom Heading Font',
                ),
                array(
                    'key' => 'field_body_font_file',
                    'label' => 'Brödtext Typsnitt (Ladda upp)',
                    'name' => 'body_font_file',
                    'type' => 'file',
                    'instructions' => 'Ladda upp .woff2, .woff eller .ttf fil för brödtext',
                    'return_format' => 'array',
                    'library' => 'all',
                    'mime_types' => 'woff2,woff,ttf,otf',
                ),
                array(
                    'key' => 'field_body_font_name',
                    'label' => 'Brödtext Typsnitt Namn',
                    'name' => 'body_font_name',
                    'type' => 'text',
                    'instructions' => 'Ange namnet på typsnittet',
                    'default_value' => 'Custom Body Font',
                ),

                // TYPOGRAFI STORLEKAR - Bättre organiserat
                array(
                    'key' => 'field_typography_sizes',
                    'label' => 'Typografi Storlekar',
                    'name' => 'typography_sizes',
                    'type' => 'group',
                    'layout' => 'block',
                    'sub_fields' => array(

                        // H1 SEKTION
                        array(
                            'key' => 'field_h1_section',
                            'label' => 'H1 - Huvudrubrik',
                            'name' => 'h1_section',
                            'type' => 'group',
                            'layout' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_h1_lg_size',
                                    'label' => 'Desktop - Storlek',
                                    'name' => 'h1_lg_size',
                                    'type' => 'text',
                                    'default_value' => '3.5rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h1_lg_height',
                                    'label' => 'Desktop - Radhöjd',
                                    'name' => 'h1_lg_height',
                                    'type' => 'text',
                                    'default_value' => '1.1',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h1_sm_size',
                                    'label' => 'Mobil - Storlek',
                                    'name' => 'h1_sm_size',
                                    'type' => 'text',
                                    'default_value' => '2.25rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h1_sm_height',
                                    'label' => 'Mobil - Radhöjd',
                                    'name' => 'h1_sm_height',
                                    'type' => 'text',
                                    'default_value' => '1.2',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                            ),
                        ),

                        // H2 SEKTION
                        array(
                            'key' => 'field_h2_section',
                            'label' => 'H2 - Underrubrik',
                            'name' => 'h2_section',
                            'type' => 'group',
                            'layout' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_h2_lg_size',
                                    'label' => 'Desktop - Storlek',
                                    'name' => 'h2_lg_size',
                                    'type' => 'text',
                                    'default_value' => '2.5rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h2_lg_height',
                                    'label' => 'Desktop - Radhöjd',
                                    'name' => 'h2_lg_height',
                                    'type' => 'text',
                                    'default_value' => '1.2',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h2_sm_size',
                                    'label' => 'Mobil - Storlek',
                                    'name' => 'h2_sm_size',
                                    'type' => 'text',
                                    'default_value' => '1.875rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h2_sm_height',
                                    'label' => 'Mobil - Radhöjd',
                                    'name' => 'h2_sm_height',
                                    'type' => 'text',
                                    'default_value' => '1.3',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                            ),
                        ),

                        // H3 SEKTION
                        array(
                            'key' => 'field_h3_section',
                            'label' => 'H3 - Sektion rubrik',
                            'name' => 'h3_section',
                            'type' => 'group',
                            'layout' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_h3_lg_size',
                                    'label' => 'Desktop - Storlek',
                                    'name' => 'h3_lg_size',
                                    'type' => 'text',
                                    'default_value' => '1.875rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h3_lg_height',
                                    'label' => 'Desktop - Radhöjd',
                                    'name' => 'h3_lg_height',
                                    'type' => 'text',
                                    'default_value' => '1.3',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h3_sm_size',
                                    'label' => 'Mobil - Storlek',
                                    'name' => 'h3_sm_size',
                                    'type' => 'text',
                                    'default_value' => '1.5rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h3_sm_height',
                                    'label' => 'Mobil - Radhöjd',
                                    'name' => 'h3_sm_height',
                                    'type' => 'text',
                                    'default_value' => '1.4',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                            ),
                        ),

                        // H4 SEKTION
                        array(
                            'key' => 'field_h4_section',
                            'label' => 'H4 - Mindre rubrik',
                            'name' => 'h4_section',
                            'type' => 'group',
                            'layout' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_h4_lg_size',
                                    'label' => 'Desktop - Storlek',
                                    'name' => 'h4_lg_size',
                                    'type' => 'text',
                                    'default_value' => '1.25rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h4_lg_height',
                                    'label' => 'Desktop - Radhöjd',
                                    'name' => 'h4_lg_height',
                                    'type' => 'text',
                                    'default_value' => '1.4',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h4_sm_size',
                                    'label' => 'Mobil - Storlek',
                                    'name' => 'h4_sm_size',
                                    'type' => 'text',
                                    'default_value' => '1.125rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_h4_sm_height',
                                    'label' => 'Mobil - Radhöjd',
                                    'name' => 'h4_sm_height',
                                    'type' => 'text',
                                    'default_value' => '1.5',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                            ),
                        ),

                        // BRÖDTEXT SEKTION
                        array(
                            'key' => 'field_p_section',
                            'label' => 'Brödtext - Paragraf',
                            'name' => 'p_section',
                            'type' => 'group',
                            'layout' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_p_lg_size',
                                    'label' => 'Desktop - Storlek',
                                    'name' => 'p_lg_size',
                                    'type' => 'text',
                                    'default_value' => '1.125rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_p_lg_height',
                                    'label' => 'Desktop - Radhöjd',
                                    'name' => 'p_lg_height',
                                    'type' => 'text',
                                    'default_value' => '1.6',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_p_sm_size',
                                    'label' => 'Mobil - Storlek',
                                    'name' => 'p_sm_size',
                                    'type' => 'text',
                                    'default_value' => '1rem',
                                    'prepend' => 'Storlek',
                                    'wrapper' => array('width' => '25'),
                                ),
                                array(
                                    'key' => 'field_p_sm_height',
                                    'label' => 'Mobil - Radhöjd',
                                    'name' => 'p_sm_height',
                                    'type' => 'text',
                                    'default_value' => '1.6',
                                    'prepend' => 'Line-height',
                                    'wrapper' => array('width' => '25'),
                                ),
                            ),
                        ),

                        // INSTRUKTIONER
                        array(
                            'key' => 'field_typography_instructions',
                            'label' => 'Instruktioner',
                            'name' => 'typography_instructions',
                            'type' => 'message',
                            'message' => '<strong>Tips:</strong><br>
                            • Använd rem-enheter för bättre skalbarhet (1rem = 16px)<br>
                            • Line-height utan enhet (1.5) är relativ till font-size<br>
                            • Line-height med enhet (24px) är absolut<br>
                            • Rekommenderad line-height för läsbarhet: 1.4-1.6',
                        ),
                    ),
                ),

                // LOGOTYP FLIK
                array(
                    'key' => 'field_branding_tab',
                    'label' => 'Logotyp',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_header_logo',
                    'label' => 'Header Logotyp',
                    'name' => 'header_logo',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_footer_logo',
                    'label' => 'Footer Logotyp',
                    'name' => 'footer_logo',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),

                // KONTAKT FLIK
                array(
                    'key' => 'field_contact_tab',
                    'label' => 'Kontakt',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_company_name',
                    'label' => 'Företagsnamn',
                    'name' => 'company_name',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_phone',
                    'label' => 'Telefon',
                    'name' => 'phone',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_email',
                    'label' => 'E-post',
                    'name' => 'email',
                    'type' => 'email',
                ),
                array(
                    'key' => 'field_address',
                    'label' => 'Adress',
                    'name' => 'address',
                    'type' => 'textarea',
                    'rows' => 3,
                ),
                array(
                    'key' => 'field_opening_hours',
                    'label' => 'Öppettider',
                    'name' => 'opening_hours',
                    'type' => 'repeater',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_weekday',
                            'label' => 'Veckodag',
                            'name' => 'weekday',
                            'type' => 'text',
                        ),
                        array(
                            'key' => 'field_hours',
                            'label' => 'Öppettider',
                            'name' => 'hours',
                            'type' => 'text',
                        ),
                    ),
                    'min' => 0,
                    'max' => 7,
                    'layout' => 'table',
                    'button_label' => 'Lägg till dag',
                ),

                // SOCIALA MEDIER FLIK
                array(
                    'key' => 'field_social_tab',
                    'label' => 'Sociala Medier',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_facebook_url',
                    'label' => 'Facebook URL',
                    'name' => 'facebook_url',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_instagram_url',
                    'label' => 'Instagram URL',
                    'name' => 'instagram_url',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_linkedin_url',
                    'label' => 'LinkedIn URL',
                    'name' => 'linkedin_url',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_youtube_url',
                    'label' => 'YouTube URL',
                    'name' => 'youtube_url',
                    'type' => 'url',
                ),

                // WOOCOMMERCE FLIK
                array(
                    'key' => 'field_woocommerce_tab',
                    'label' => 'WooCommerce',
                    'name' => '',
                    'type' => 'tab',
                    'placement' => 'left',
                ),
                array(
                    'key' => 'field_single_product_style',
                    'label' => 'Produktsida Layout',
                    'name' => 'single_product_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_archive_product_style',
                    'label' => 'Produktarkiv Layout',
                    'name' => 'archive_product_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_checkout_style',
                    'label' => 'Checkout Layout',
                    'name' => 'checkout_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_cart_style',
                    'label' => 'Varukorg Layout',
                    'name' => 'cart_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_myaccount_style',
                    'label' => 'Mitt Konto Layout',
                    'name' => 'myaccount_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
                array(
                    'key' => 'field_auth_style',
                    'label' => 'Logga In/Registrera Layout',
                    'name' => 'auth_style',
                    'type' => 'select',
                    'choices' => array(
                        'style1' => 'Style 1',
                    ),
                    'default_value' => 'style1',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'theme-settings',
                    ),
                ),
            ),
        ));
    }
}
add_action('acf/init', 'register_theme_settings_fields');

// Helper function för att hämta tema-inställningar
function get_theme_option($field_name, $default = '')
{
    return get_field($field_name, 'option') ?: $default;
}
