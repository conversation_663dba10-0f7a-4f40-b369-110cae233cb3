/* ==========================================================================
   CONTAINER SYSTEM
   ========================================================================== */

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);


  /* Add a data key based custom width overwrite */
  &[data-width] {
    max-width: attr(data-width px);
  }
}

.sub-menu {
   height:0;
}
.sub-menu[aria-extended="true"] {
  height:auto
}


@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Container Variants */
.container-sm {
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.container-md {
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.container-lg {
  max-width: 1024px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.container-xl {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.container-fluid {
  width: 100%;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}