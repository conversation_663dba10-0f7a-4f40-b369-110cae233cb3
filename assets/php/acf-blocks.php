<?php

// Register sitebuilder field group för alla sidor och inlägg
function register_sitebuilder_field_group()
{

    // Hämta alla ACF Blocks för att lägga till i sitebuilder
    $acf_blocks = [];
    $excluded_blocks = ['theme_settings', 'sitebuilder'];

    if (function_exists('acf_add_local_field_group')) {
        $all_acf_blocks = acf_get_field_groups();

        foreach ($all_acf_blocks as $block) {
            if ($block['location'][0][0]['param'] !== 'block') {
                continue;
            }
            if (in_array($block['name'], $excluded_blocks)) {
                continue;
            }
            $block_name = $block['name'];
            $acf_blocks[$block_name] = $block;
        }
    }



    if (function_exists('acf_add_local_field_group')) {

        acf_add_local_field_group(array(
            'key' => 'group_sitebuilder',
            'title' => 'Sitebuilder',
            'name' => 'sitebuilder',
            'fields' => array(
                array(
                    'key' => 'field_sitebuilder',
                    'label' => 'Sitebuilder',
                    'name' => 'sitebuilder',
                    'type' => 'flexible_content',
                    'layouts' => array(
                        $acf_blocks,
                        'text_block' => array(
                            'key' => 'layout_text_block',
                            'name' => 'text_block',
                            'label' => 'Text Block',
                            'display' => 'block',
                            'sub_fields' => array(
                                array(
                                    'key' => 'field_text_heading',
                                    'label' => 'Rubrik',
                                    'name' => 'heading',
                                    'type' => 'text',
                                ),
                                array(
                                    'key' => 'field_text_content',
                                    'label' => 'Innehåll',
                                    'name' => 'content',
                                    'type' => 'wysiwyg',
                                    'toolbar' => 'full',
                                    'media_upload' => 1,
                                ),
                                array(
                                    'key' => 'field_text_alignment',
                                    'label' => 'Textjustering',
                                    'name' => 'alignment',
                                    'type' => 'select',
                                    'choices' => array(
                                        'left' => 'Vänster',
                                        'center' => 'Centrerad',
                                        'right' => 'Höger',
                                    ),
                                    'default_value' => 'left',
                                ),
                                array(
                                    'key' => 'field_text_background',
                                    'label' => 'Bakgrundsfärg',
                                    'name' => 'background_color',
                                    'type' => 'color_picker',
                                    'default_value' => '#ffffff',
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'page',
                    ),
                ),
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'post',
                    ),
                ),
            ),
        ));
    }
}
add_action('acf/init', 'register_sitebuilder_field_group');

// Dölj standard editor för pages och posts
function hide_editor_for_pages()
{
    remove_post_type_support('page', 'editor');
    remove_post_type_support('post', 'editor');
}
add_action('init', 'hide_editor_for_pages');
