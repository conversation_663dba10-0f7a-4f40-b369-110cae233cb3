<?php
defined('ABSPATH') || exit;

global $product;

// Säkerställ synlighet.
if (empty($product) || ! $product->is_visible()) {
  return;
}
/**
 * Product Item Component
 * 
 * @param string $example Example prop
 * 
 */

/*
* Definiera props och fallbacks
*/
// $example = $props['key'] ?? 'default';
$type = $props['type'] ?? 'primary';
$size = $props['size'] ?? 'medium';
$permalink = get_permalink($product->get_id()) ?? '';

// Product props med Woocommerce fallback
$product_id = $props['product_id'] ?? $product->get_id() ?? '';
$product_name = $props['title'] ?? get_the_title($product_id) ?? '';
$product_price = $props['price'] ?? $product->get_price_html() ?? '';
$product_image = $props['image'] ?? get_the_post_thumbnail($product_id, 'full') ?? '';

/*
* Hämta Lagerstatus
*/
$stock_status = $product->get_stock_status();
$backorders = $product->backorders_allowed();
$stock_text = '';
$stock_class = '';

if ($stock_status === 'instock') {
  $stock_text = 'Leverans 1-2 dagar';
  $stock_class = 'stock-green';
} elseif ($stock_status === 'onbackorder') {
  $stock_text = 'Leverans 4-7 dagar';
  // $stock_class = 'stock-yellow';
  $stock_class = 'stock-yelow';
} else {
  $stock_text = 'Slut i lager';
  $stock_class = 'stock-red';
}

// Bygg CSS Klasser
$prefix = 'product-item';
$css_classes = [$prefix, $prefix . '-' . $type, $prefix . '-' . $size];

// Get product-item.css styling
wp_enqueue_style('product-item-style', get_stylesheet_directory_uri() . '/components/product-item/product-item.css', array(), '1.0', 'all');

// Render the component
?>



<li <?= wc_product_class($css_classes, $product); ?>>
  <div class="<?= $prefix . '-card' ?>">
    <?php
    /**
     * Hook: woocommerce_before_shop_loop_item.
     *
     * @hooked woocommerce_template_loop_product_link_open - 10
     */
    do_action('woocommerce_before_shop_loop_item');

    /**
     * Hook: woocommerce_before_shop_loop_item_title.
     *
     * @hooked woocommerce_template_loop_product_thumbnail - 10
     */
    do_action('woocommerce_before_shop_loop_item_title');
    ?>


    <div class="<?= $prefix . '-info' ?>">

      <div class="<?= $prefix . '-title-container' ?>">
        <?php
        /**
         * Hook: woocommerce_shop_loop_item_title.
         *
         * @hooked woocommerce_template_loop_product_title - 10
         */
        do_action('woocommerce_shop_loop_item_title');

        ?>

      </div>

      <!-- Lagerstatus -->
      <div class="stock-status <?php echo esc_attr($stock_class); ?>">
        <span class="stock-dot"></span> <?php echo esc_html($stock_text); ?>
      </div>

      <?php
      /**
       * Hook: woocommerce_after_shop_loop_item_title.
       *
       * @hooked woocommerce_template_loop_price - 10
       */
      do_action('woocommerce_after_shop_loop_item_title');


      /**
       * Hook: woocommerce_after_shop_loop_item.
       *
       * @hooked woocommerce_template_loop_product_link_close - 5
       * @hooked woocommerce_template_loop_add_to_cart - 10
       */
      do_action('woocommerce_after_shop_loop_item');
      ?>

      <!-- Köp produkt-knapp -->
      <?php _sc('button', [
        'text' => 'Köp produkt',
        'href' => $permalink,
        'class' => 'buy-product-button'
      ]) ?>
    </div>
  </div>
</li>