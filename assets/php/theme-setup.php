<?php

// Add theme support
add_theme_support('post-thumbnails');
add_theme_support('title-tag');
add_theme_support('custom-logo');
add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));

// Register navigation menus
function register_theme_menus()
{
  register_nav_menus(array(
    'header-menu' => __('Header Menu'),
    'footer-menu' => __('Footer Menu'),
  ));
}
add_action('init', 'register_theme_menus');

// Enqueue scripts and styles
function theme_assets()
{
  wp_enqueue_style('main-css', get_template_directory_uri() . '/assets/css/main.css');
  wp_enqueue_style('components-css', get_template_directory_uri() . '/assets/css/components.css');

  // Load as ES6 module
  wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', array(), '1.0.0', true);
  wp_script_add_data('main-js', 'type', 'module');
}
add_action('wp_enqueue_scripts', 'theme_assets');

// Lägg till CSS-variabler från ACF-fält
function add_theme_css_variables()
{
  // Hämta färger från ACF
  $primary_color = get_field('primary_color', 'option') ?: '#000000';
  $secondary_color = get_field('secondary_color', 'option') ?: '#ffffff';
  $accent_color = get_field('accent_color', 'option') ?: '#B6B09F';
  $text_color = get_field('text_color', 'option') ?: '#333333';
  $background_color = get_field('background_color', 'option') ?: '#ffffff';

  // Hämta typografi från ACF
  $typography = get_field('typography_sizes', 'option');
  $heading_font_name = get_field('heading_font_name', 'option') ?: 'Prompt';
  $body_font_name = get_field('body_font_name', 'option') ?: 'Prompt';

  // Överskrid CSS-variabler med ACF-värden
  echo '<style>
    :root {
      --primary-color: ' . $primary_color . ';
      --secondary-color: ' . $secondary_color . ';
      --accent-color: ' . $accent_color . ';
      --text-color: ' . $text_color . ';
      --background-color: ' . $background_color . ';
      --heading-font: "' . $heading_font_name . '", sans-serif;
      --body-font: "' . $body_font_name . '", sans-serif;';

  // Lägg till typografi-storlekar om de finns
  if ($typography) {
    echo '
      --h1-lg-size: ' . ($typography['h1_section']['h1_lg_size'] ?? '80px') . ';
      --h1-lg-height: ' . ($typography['h1_section']['h1_lg_height'] ?? '1.1') . ';
      --h1-sm-size: ' . ($typography['h1_section']['h1_sm_size'] ?? '45px') . ';
      --h1-sm-height: ' . ($typography['h1_section']['h1_sm_height'] ?? '1.2') . ';
      --h2-lg-size: ' . ($typography['h2_section']['h2_lg_size'] ?? '65px') . ';
      --h2-lg-height: ' . ($typography['h2_section']['h2_lg_height'] ?? '1.2') . ';
      --h2-sm-size: ' . ($typography['h2_section']['h2_sm_size'] ?? '30px') . ';
      --h2-sm-height: ' . ($typography['h2_section']['h2_sm_height'] ?? '1.3') . ';
      --h3-lg-size: ' . ($typography['h3_section']['h3_lg_size'] ?? '25px') . ';
      --h3-lg-height: ' . ($typography['h3_section']['h3_lg_height'] ?? '1.3') . ';
      --h3-sm-size: ' . ($typography['h3_section']['h3_sm_size'] ?? '24px') . ';
      --h3-sm-height: ' . ($typography['h3_section']['h3_sm_height'] ?? '1.4') . ';
      --h4-lg-size: ' . ($typography['h4_section']['h4_lg_size'] ?? '22px') . ';
      --h4-lg-height: ' . ($typography['h4_section']['h4_lg_height'] ?? '1.4') . ';
      --h4-sm-size: ' . ($typography['h4_section']['h4_sm_size'] ?? '20px') . ';
      --h4-sm-height: ' . ($typography['h4_section']['h4_sm_height'] ?? '1.5') . ';
      --h5-lg-size: ' . ($typography['h5_section']['h5_lg_size'] ?? '18px') . ';
      --h5-lg-height: ' . ($typography['h5_section']['h5_lg_height'] ?? '1.2') . ';
      --h5-sm-size: ' . ($typography['h5_section']['h5_sm_size'] ?? '16px') . ';
      --h5-sm-height: ' . ($typography['h5_section']['h5_sm_height'] ?? '1.2') . ';
      --h6-lg-size: ' . ($typography['h6_section']['h6_lg_size'] ?? '20px') . ';
      --h6-lg-height: ' . ($typography['h6_section']['h6_lg_height'] ?? '1.2') . ';
      --h6-sm-size: ' . ($typography['h6_section']['h6_sm_size'] ?? '14px') . ';
      --h6-sm-height: ' . ($typography['h6_section']['h6_sm_height'] ?? '1.2') . ';
      --p-lg-size: ' . ($typography['p_section']['p_lg_size'] ?? '20px') . ';
      --p-lg-height: ' . ($typography['p_section']['p_lg_height'] ?? '1.6') . ';
      --p-sm-size: ' . ($typography['p_section']['p_sm_size'] ?? '16px') . ';
      --p-sm-height: ' . ($typography['p_section']['p_sm_height'] ?? '1.6') . ';';
  }

  echo '
    }
  </style>';

  // Ladda uppladdade typsnitt
  $heading_font_file = get_field('heading_font_file', 'option');
  $body_font_file = get_field('body_font_file', 'option');

  if ($heading_font_file && isset($heading_font_file['url'])) {
    echo '<style>
      @font-face {
        font-family: "' . $heading_font_name . '";
        src: url("' . $heading_font_file['url'] . '");
        font-display: swap;
      }
    </style>';
  }

  if ($body_font_file && isset($body_font_file['url'])) {
    echo '<style>
      @font-face {
        font-family: "' . $body_font_name . '";
        src: url("' . $body_font_file['url'] . '");
        font-display: swap;
      }
    </style>';
  }
}
add_action('wp_head', 'add_theme_css_variables');

// Remove admin bar for non-admins
if (!current_user_can('administrator')) {
  add_filter('show_admin_bar', '__return_false');
}

// Lägg till stöd för font-filer i WordPress
function add_font_mime_types($mimes)
{
  $mimes['woff'] = 'font/woff';
  $mimes['woff2'] = 'font/woff2';
  $mimes['ttf'] = 'font/ttf';
  $mimes['otf'] = 'font/otf';
  $mimes['eot'] = 'application/vnd.ms-fontobject';
  return $mimes;
}
add_filter('upload_mimes', 'add_font_mime_types');

// Tillåt font-filer att laddas upp (säkerhet)
function allow_font_uploads($data, $file, $filename, $mimes)
{
  $wp_filetype = wp_check_filetype($filename, $mimes);
  $ext = $wp_filetype['ext'];
  $type = $wp_filetype['type'];

  // Kontrollera om det är en font-fil
  if (in_array($ext, ['woff', 'woff2', 'ttf', 'otf', 'eot'])) {
    $data['ext'] = $ext;
    $data['type'] = $type;
  }

  return $data;
}
add_filter('wp_check_filetype_and_ext', 'allow_font_uploads', 10, 4);

// Lägg till font-filer i media library filter
function add_font_post_mime_types($post_mime_types)
{
  $post_mime_types['font/woff'] = array(__('WOFF Fonts'), __('Manage WOFF Fonts'), _n_noop('WOFF Font <span class="count">(%s)</span>', 'WOFF Fonts <span class="count">(%s)</span>'));
  $post_mime_types['font/woff2'] = array(__('WOFF2 Fonts'), __('Manage WOFF2 Fonts'), _n_noop('WOFF2 Font <span class="count">(%s)</span>', 'WOFF2 Fonts <span class="count">(%s)</span>'));
  $post_mime_types['font/ttf'] = array(__('TTF Fonts'), __('Manage TTF Fonts'), _n_noop('TTF Font <span class="count">(%s)</span>', 'TTF Fonts <span class="count">(%s)</span>'));
  $post_mime_types['font/otf'] = array(__('OTF Fonts'), __('Manage OTF Fonts'), _n_noop('OTF Font <span class="count">(%s)</span>', 'OTF Fonts <span class="count">(%s)</span>'));

  return $post_mime_types;
}
add_filter('post_mime_types', 'add_font_post_mime_types');

// Fixa för variable fonts med komplexa filnamn
function sanitize_font_filename($filename)
{
  // Ta bort problematiska tecken från font-filnamn
  $filename = preg_replace('/[^a-zA-Z0-9._-]/', '-', $filename);
  // Ta bort flera bindestreck i rad
  $filename = preg_replace('/-+/', '-', $filename);

  return $filename;
}
add_filter('sanitize_file_name', 'sanitize_font_filename');
